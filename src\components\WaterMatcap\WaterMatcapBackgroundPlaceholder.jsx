import React from "react";
import { Canvas } from "@react-three/fiber";
import { Scene } from "./scene_production";

export default function WaterMatcapBackgroundPlaceholder({ position = [0,0,0], ripples = [] }) {
  return (
    <Canvas 
      camera={{ position: [0, 0, 5], fov: 60 }}
      style={{ width: '100vw', height: '100vh' }}
      gl={{ antialias: true }}
      onPointerMove={(e) => {
        // Ensure pointer events are captured across the entire viewport
        e.stopPropagation();
      }}
    >
      <Scene position={position} ripples={ripples} />
    </Canvas>
  );
} 