import { useEffect, useMemo } from "react";
import { useThree } from "@react-three/fiber";
import { useCameraStore, mainCamera, orbitCamera } from "./scene_copy/cameras2";

export function Cameras() {
  const { set: threeSet } = useThree();
  
  // Production settings - no controls, use default values
  const camera = "main"; // Default to main camera

  // Camera mapping - memoize to prevent infinite loops
  const cameraMap = useMemo(() => ({
    main: mainCamera,
    orbit: orbitCamera,
  }), []);

  useEffect(() => {
    mainCamera.lookAt(0, 0, 0);

    // Set orbit camera to original position initially
    orbitCamera.position.set(0, 0.35, 2);
    orbitCamera.rotation.set(-0.12, 0, 0);

    const selectedCamera = cameraMap[camera];
    // Update the camera store so the Scene component knows which camera is active
    useCameraStore.getState().set(selectedCamera);
    selectedCamera.updateProjectionMatrix();
    // Update Three.js state to use this camera
    threeSet({ camera: selectedCamera });
  }, [threeSet, cameraMap, camera]);

  const width = useThree((state) => state.size.width);
  const height = useThree((state) => state.size.height);

  useEffect(() => {
    mainCamera.aspect = width / height;
    mainCamera.updateProjectionMatrix();
    orbitCamera.aspect = width / height;
    orbitCamera.updateProjectionMatrix();
  }, [width, height]);

  return (
    <>
      <primitive
        object={cameraMap.main}
        position={[0, 3, 0]}
        fov={40}
        near={0.1}
        far={10}
        aspect={width / height}
      />
      <primitive
        object={cameraMap.orbit}
        near={0.1}
        far={7}
        aspect={width / height}
      />
    </>
  );
}

// Export the camera store and cameras for use in other components
export { useCameraStore, mainCamera, orbitCamera } from "./scene_copy/cameras2";
