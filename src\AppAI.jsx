import { Suspense, useState, useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useControls } from 'leva';
import { ReactLenis, useLenis } from 'lenis/react';
import './AppAI.css';

// Import modular components
import SceneManager from './components/SceneManager/SceneManager';
import Scene1 from './components/SceneManager/Scene1';
import Scene2 from './components/SceneManager/Scene2';
import Scene3 from './components/SceneManager/Scene3';
import CameraController from './components/SceneManager/CameraController';
import Scene1Overlay from './components/SceneManager/Scene1Overlay';
import PerformanceAnalyzer from './components/Performance/PerformanceAnalyzer';
import OptimizationGuide from './components/Performance/OptimizationGuide';

function LenisController({ onScrollUpdate }) {
  const lenis = useLenis(({ scroll, progress }) => {
    // Infinite loop logic
    const scrollableHeight = lenis.limit;
    let currentProgress = progress;

    if (progress >= 1.0) {
      // When reaching the end, seamlessly loop back to the start
      const newScroll = scroll - scrollableHeight;
      lenis.scrollTo(newScroll, { immediate: true });
      currentProgress = newScroll / scrollableHeight;
    } else if (progress < 0) {
      // When reaching the start (scrolling up), loop to the end
      const newScroll = scroll + scrollableHeight;
      lenis.scrollTo(newScroll, { immediate: true });
      currentProgress = newScroll / scrollableHeight;
    }
    onScrollUpdate(currentProgress);
  });

  return null;
}

export default function AppAI() {
  // State for scroll progress and scene management
  const [globalScrollProgress, setGlobalScrollProgress] = useState(0);
  const [currentScene, setCurrentScene] = useState(1);
  const [transitionProgress, setTransitionProgress] = useState(0);
  const [transitionType, setTransitionType] = useState('');
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Leva controls for debugging
  const { showDebugInfo } = useControls('App Controls', {
    showDebugInfo: false,
  });

  // Reset scroll on page load
  useEffect(() => {
    window.scrollTo(0, 0);
    setGlobalScrollProgress(0);
  }, []);

  // Scene boundaries definition - Adjusted for slower transitions and Scene2 scrolling
  const sceneBoundaries = {
    scene1: { start: 0.0, end: 0.20 },      // Scene 1 takes 20% of the scroll
    transition1: { start: 0.20, end: 0.30 }, // Transition from 1 to 2 is slower (10%)
    scene2: { start: 0.30, end: 0.80 },      // Scene 2 is much slower (50%)
    transition2: { start: 0.80, end: 0.85 }, // Transition from 2 to 3 (5%)
    scene3: { start: 0.85, end: 0.90 },      // Scene 3 takes 5%
    transition3: { start: 0.90, end: 1.0 },      // Transition from 3 to 1 is much slower (10%)
  };

  // Determine current scene and transition progress based on global scroll
  const { scene, transition, type, isTrans } = (() => {
    const p = globalScrollProgress;
    if (p >= sceneBoundaries.transition3.start) return { scene: 3, transition: (p - sceneBoundaries.transition3.start) / (sceneBoundaries.transition3.end - sceneBoundaries.transition3.start), type: 'scene3to1', isTrans: true };
    if (p >= sceneBoundaries.scene3.start) return { scene: 3, transition: 0, type: '', isTrans: false };
    if (p >= sceneBoundaries.transition2.start) return { scene: 2, transition: (p - sceneBoundaries.transition2.start) / (sceneBoundaries.transition2.end - sceneBoundaries.transition2.start), type: 'scene2to3', isTrans: true };
    if (p >= sceneBoundaries.scene2.start) return { scene: 2, transition: 0, type: '', isTrans: false };
    if (p >= sceneBoundaries.transition1.start) return { scene: 1, transition: (p - sceneBoundaries.transition1.start) / (sceneBoundaries.transition1.end - sceneBoundaries.transition1.start), type: 'scene1to2', isTrans: true };
    return { scene: 1, transition: 0, type: '', isTrans: false };
  })();

  const scene1Progress = (globalScrollProgress <= sceneBoundaries.scene1.end) ? globalScrollProgress / sceneBoundaries.scene1.end : 1;
  const scene2Progress = (globalScrollProgress >= sceneBoundaries.scene2.start && globalScrollProgress <= sceneBoundaries.scene2.end) ? (globalScrollProgress - sceneBoundaries.scene2.start) / (sceneBoundaries.scene2.end - sceneBoundaries.scene2.start) : (globalScrollProgress > sceneBoundaries.scene2.end ? 1 : 0);
  const scene3Progress = (globalScrollProgress >= sceneBoundaries.scene3.start && globalScrollProgress <= sceneBoundaries.scene3.end) ? (globalScrollProgress - sceneBoundaries.scene3.start) / (sceneBoundaries.scene3.end - sceneBoundaries.scene3.start) : (globalScrollProgress > sceneBoundaries.scene3.end ? 1 : 0);

  return (
    <ReactLenis root options={{ lerp: 0.08, duration: 1.5, smoothTouch: true, wheelMultiplier: 0.6 }}>
      <div style={{ position: 'relative', width: '100vw', height: '500vh' }}>
        <div style={{ position: 'sticky', top: 0, width: '100vw', height: '100vh', overflow: 'hidden' }}>
          <Canvas
            camera={{ position: [0, 0.35, 2], fov: 45 }}
            gl={{ alpha: true, antialias: true, powerPreference: 'high-performance' }}
            style={{ position: 'absolute', top: 0, left: 0, width: '100vw', height: '100vh', zIndex: 0 }}
          >
            <Suspense fallback={null}>
              <LenisController onScrollUpdate={setGlobalScrollProgress} />
              <CameraController />
              <SceneManager
                Scene1Component={Scene1}
                Scene2Component={Scene2}
                Scene3Component={Scene3}
                transition={transition}
                transitionType={type}
                globalScrollProgress={globalScrollProgress}
                scene1Progress={scene1Progress}
                scene2Progress={scene2Progress}
                scene3Progress={scene3Progress}
                currentScene={scene}
                isTransitioning={isTrans}
              />
            </Suspense>
          </Canvas>
          <Scene1Overlay
            globalScrollProgress={globalScrollProgress}
            transitionType={type}
            transitionProgress={transition}
            scrollProgress={scene1Progress}
            isVisible={scene === 1 && !isTrans}
          />
          <PerformanceAnalyzer />
          <OptimizationGuide />
          {showDebugInfo && (
            <div style={{
              position: 'absolute', top: '50px', right: '10px', background: 'rgba(0,0,0,0.8)',
              color: 'lime', padding: '10px', borderRadius: '5px', fontFamily: 'monospace',
              fontSize: '12px', zIndex: 1000
            }}>
              <div>Global Scroll: {(globalScrollProgress * 100).toFixed(1)}%</div>
              <div>Current Scene: {currentScene}</div>
              <div>Transition: {(transitionProgress * 100).toFixed(1)}%</div>
              <div>Transition Type: {transitionType}</div>
              <div>Is Transitioning: {isTransitioning ? 'Yes' : 'No'}</div>
              <div>Scene1 Progress: {(scene1Progress * 100).toFixed(1)}%</div>
              <div>Scene2 Progress: {(scene2Progress * 100).toFixed(1)}%</div>
              <div>Scene3 Progress: {(scene3Progress * 100).toFixed(1)}%</div>
            </div>
          )}
        </div>
      </div>
    </ReactLenis>
  );
}
