import React, { useEffect, useRef, useState, Suspense, useCallback, useMemo } from 'react';
import { Canvas } from '@react-three/fiber';
import { Fluid } from '@whatisjery/react-fluid-distortion';
import { EffectComposer } from '@react-three/postprocessing';
import { Color } from 'three';
import Lenis from 'lenis';
import gsap from 'gsap'; // Import GSAP
import AnimatedText from './components/AnimatedText'; // Import the AnimatedText component
import styles from './ProjectPage.module.css'; // Updated import for CSS modules

// Performance detection utility
const detectDevicePerformance = () => {
  // Check for hardware concurrency (CPU cores)
  const cpuCores = navigator.hardwareConcurrency || 2;
  
  // Check for device memory (in GB)
  const deviceMemory = navigator.deviceMemory || 2;
  
  // Check for GPU capabilities
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  let gpuTier = 'low';
  
  if (gl) {
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    if (debugInfo) {
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
      
      // Detect high-end GPUs
      if (renderer.includes('RTX') || renderer.includes('GTX 1080') || renderer.includes('GTX 1660') || 
          renderer.includes('RX 580') || renderer.includes('RX 5700') || renderer.includes('Vega') ||
          renderer.includes('M1') || renderer.includes('M2') || renderer.includes('M3')) {
        gpuTier = 'high';
      } else if (renderer.includes('Intel') || renderer.includes('UHD') || renderer.includes('HD')) {
        gpuTier = 'low';
      } else {
        gpuTier = 'medium';
      }
    }
  }
  
  // Check for mobile devices
  const isMobile = /iPhone|iPad|iPod|Android|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  
  // Check for connection speed
  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
  const isSlowConnection = connection ? 
    (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') : false;
  
  // Calculate performance score (0-100)
  let score = 0;
  
  // CPU score (0-30 points)
  if (cpuCores >= 8) score += 30;
  else if (cpuCores >= 6) score += 25;
  else if (cpuCores >= 4) score += 20;
  else if (cpuCores >= 2) score += 10;
  
  // Memory score (0-25 points)
  if (deviceMemory >= 8) score += 25;
  else if (deviceMemory >= 4) score += 20;
  else if (deviceMemory >= 2) score += 15;
  else score += 10;
  
  // GPU score (0-25 points)
  if (gpuTier === 'high') score += 25;
  else if (gpuTier === 'medium') score += 20;
  else score += 10;
  
  // Mobile penalty (-20 points)
  if (isMobile) score -= 20;
  
  // Connection penalty (-10 points)
  if (isSlowConnection) score -= 10;
  
  // Determine performance tier
  let tier = 'low';
  if (score >= 70) tier = 'high';
  else if (score >= 40) tier = 'medium';
  
  return {
    tier,
    score,
    cpuCores,
    deviceMemory,
    gpuTier,
    isMobile,
    isSlowConnection,
    supportsFluid: tier === 'high' && !isMobile
  };
};

// Manual FluidEffect component - no automated movements for better performance
const ManualFluidEffect = React.memo(() => {
  return null; // Only manual interactions now
});

// Video management component for controlling lazy-loaded videos with adaptive quality
const LazyVideo = React.memo(React.forwardRef(({ 
  src, 
  type, 
  autoPlay = false, // Changed to false for hover-only playback
  loop = true, 
  muted = true, 
  controls = false, 
  className = "", 
  poster = null,

  ...props 
}, ref) => {
  const videoRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [quality, setQuality] = useState('high');
  const [preload, setPreload] = useState('none'); // Changed to 'none' for better performance
  const [source, setSource] = useState('');
  const observerRef = useRef(null);
  const hoverTimeoutRef = useRef(null);

  // Handle video source based on quality - OPTIMIZED to prevent remounting
  useEffect(() => {
    if (!src || !videoRef.current) return;
    
    // Update video source without remounting the component
    if (videoRef.current.src !== src) {
      videoRef.current.src = src;
      // Reset video to beginning if it was already playing
      if (videoRef.current.currentTime > 0) {
        videoRef.current.currentTime = 0;
      }
    }
  }, [src]); // Remove quality dependency to prevent remounting

  // Setup intersection observer with rootMargin to start loading before entering viewport
  useEffect(() => {
    if (!videoRef.current) return;

    const options = {
      root: null,
      rootMargin: '20% 0px 20% 0px', // Start loading when within 20% of viewport
      threshold: 0.1
    };

    const handleIntersect = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Don't autoplay - only on hover
        } else {
          // Pause video when out of view
          if (videoRef.current && !videoRef.current.paused) {
            videoRef.current.pause();
            // Keep current position - don't reset to beginning
          }
          setIsVisible(false);
        }
      });
    };

    observerRef.current = new IntersectionObserver(handleIntersect, options);
    observerRef.current.observe(videoRef.current);

    return () => {
      if (observerRef.current && videoRef.current) {
        observerRef.current.unobserve(videoRef.current);
      }
      if (videoRef.current) {
        videoRef.current.pause();
        videoRef.current.src = ''; // Release video resources
      }
    };
  }, []);

  // Handle quality and preload based on screen size and network conditions
  useEffect(() => {
    const handleResize = () => {
      const screenWidth = window.innerWidth;
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      const isSlowConnection = connection ? 
        (connection.effectiveType === 'slow-2g' || 
         connection.effectiveType === '2g' || 
         connection.saveData === true) : false;
      
      // Adjust quality based on screen size and connection
      if (screenWidth < 768 || isMobile || isSlowConnection) {
        setQuality('low');
        setPreload('none');
      } else if (screenWidth < 1200) {
        setQuality('medium');
        setPreload('metadata');
      } else {
        setQuality('high');
        setPreload('metadata'); // Keep as metadata for hover functionality
      }
    };

    // Add event listeners with proper cleanup
    const controller = new AbortController();
    window.addEventListener('resize', handleResize, { signal: controller.signal });
    
    // Check for connection changes
    if (navigator.connection) {
      navigator.connection.addEventListener('change', handleResize, { signal: controller.signal });
    }
    
    // Initial setup
    handleResize();
    
    // Cleanup
    return () => {
      controller.abort();
    };
  }, []);

  // Hover handlers for video playback
  const handleMouseEnter = useCallback(() => {
    if (!isVisible || !videoRef.current) return;
    
    setIsHovered(true);
    

    
    // Start playing after a short delay
    hoverTimeoutRef.current = setTimeout(() => {
      if (videoRef.current && isVisible) {
        const playPromise = videoRef.current.play();
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            console.log("Video play failed:", error);
          });
        }
      }
    }, 200);
  }, [isVisible]);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    

    
    // Clear hover timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    
    // Pause video when mouse leaves - DON'T reset to beginning!
    if (videoRef.current && !videoRef.current.paused) {
      videoRef.current.pause();
    }
  }, []);

  // Combine refs properly
  const setRefs = React.useCallback((node) => {
    // Set the ref for the video element
    videoRef.current = node;
    
    // Handle the forwarded ref
    if (typeof ref === 'function') {
      ref(node);
    } else if (ref) {
      ref.current = node;
    }
  }, [ref]);

  // Responsive sources
  const sources = [
    {
      src: source,
      type: type || 'video/mp4',
      quality: quality,
    },
  ];

  // Render the video element with optimized attributes
  return (
    <div 
      className={`${styles.videoContainer} ${isVisible ? styles.visible : ''} ${isHovered ? styles.hovered : ''}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <video
        ref={setRefs}
        preload={preload}
        autoPlay={false} // Never autoplay - only on hover
        loop={loop}
        muted={muted}
        playsInline
        controls={controls}
        className={`${styles.video} ${className}`}
        disablePictureInPicture
        disableRemotePlayback
        poster={poster}
        {...props}
      >
        {isVisible && sources.map((source, index) => (
          <source key={index} src={source.src} type={source.type} />
        ))}
        Your browser does not support the video tag.
      </video>
      {!isVisible && (
        <div className={styles.videoPlaceholder}>
          {/* Optional: Add a loading spinner or thumbnail here */}
        </div>
      )}
      {isVisible && !isHovered && (
        <div className={styles.hoverIndicator}>
          <span>Hover to play</span>
        </div>
      )}
    </div>
  );
}));

function ProjectPage({ projectId, onBack, onReady, projectsData }) {
  const project = projectsData && projectsData[projectId];
  const contentRef = useRef(null); // Ref for the scroll container
  const contentWrapperRef = useRef(null); // Ref for the content wrapper with fixed height
  const lenisRef = useRef(null);
  const rafRef = useRef(null);
  const lastProjectImageRef = useRef(null);
  const footerSectionRef = useRef(null);
  const footerContentRef = useRef(null); // Add ref for footer content
  
  // Add refs for the back button, about button, and hero image
  const backButtonRef = useRef(null);
  const aboutButtonRef = useRef(null);
  const heroImageRef = useRef(null);
  
  // Use refs instead of state for non-visual data to prevent re-renders
  const contentHeightRef = useRef(0);
  const scrollProgressRef = useRef(0);
  const isDarkModeRef = useRef(false);
  
  const [scrollProgress, setScrollProgress] = useState(0);
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  // Added state to control animation start
  const [startAnimation, setStartAnimation] = useState(false);
  
  // Add state to track mouse interaction for Fluid effect
  const [isMouseInteracting, setIsMouseInteracting] = useState(false);
  const mouseInteractionTimeoutRef = useRef(null);
  
  // Performance detection state
  const [devicePerformance, setDevicePerformance] = useState(null);
  const [supportsFluid, setSupportsFluid] = useState(true); // Default to true, will be updated after detection
  

  
  // Mouse interaction handler for Fluid effect
  const handleMouseInteraction = useCallback(() => {
    setIsMouseInteracting(true);
    
    // Clear existing timeout
    if (mouseInteractionTimeoutRef.current) {
      clearTimeout(mouseInteractionTimeoutRef.current);
    }
    
    // Set timeout to stop rendering after mouse stops moving
    mouseInteractionTimeoutRef.current = setTimeout(() => {
      setIsMouseInteracting(false);
    }, 1000); // Stop rendering 1 second after last mouse movement
  }, []);





  // Detect device performance on mount
  useEffect(() => {
    const performance = detectDevicePerformance();
    setDevicePerformance(performance);
    setSupportsFluid(performance.supportsFluid);
    
    // Log performance info for debugging
    console.log('Device Performance:', {
      tier: performance.tier,
      score: performance.score,
      cpuCores: performance.cpuCores,
      deviceMemory: performance.deviceMemory + 'GB',
      gpuTier: performance.gpuTier,
      isMobile: performance.isMobile,
      supportsFluid: performance.supportsFluid
    });
  }, []);

  
  
  // Scroll handler wrapper for Lenis - defined outside useEffect for proper cleanup
  const handleScrollWrapper = useCallback(() => {
    // Direct scroll handling without calling handleScroll to prevent infinite loop
    if (!lastProjectImageRef.current || !footerSectionRef.current) return;
    
    const scrollY = lenisRef.current.scroll;
    const viewportHeight = window.innerHeight;
    
    // Calculate positions
    const lastImagePosition = lastProjectImageRef.current.offsetTop;
    const lastImageHeight = lastProjectImageRef.current.offsetHeight;
    const footerPosition = footerSectionRef.current.offsetTop;
    
    // Calculate scroll progress for the last image
    const lastImageScrollStart = lastImagePosition - viewportHeight;
    const lastImageScrollEnd = lastImagePosition + lastImageHeight;
    const lastImageScrollRange = lastImageScrollEnd - lastImageScrollStart;
    
    // Calculate footer opacity based on scroll position
    const footerScrollStart = footerPosition - viewportHeight;
    const footerScrollRange = viewportHeight;
    
    if (footerScrollRange > 0 && footerContentRef.current) {
      const footerProgress = Math.max(0, Math.min(1, (scrollY - footerScrollStart) / footerScrollRange));
      const footerOpacity = Math.max(0, Math.min(1, footerProgress));
      
      // Use CSS custom property for smooth transitions
      footerContentRef.current.style.setProperty('--footer-opacity', footerOpacity.toString());
    }
    
    // Determine dark mode based on scroll progress
    if (lastImageScrollRange > 0) {
      const lastImageProgress = Math.max(0, Math.min(1, (scrollY - lastImageScrollStart) / lastImageScrollRange));
      setScrollProgress(lastImageProgress);
      
      const shouldBeDark = lastImageProgress > 0.5;
      if (shouldBeDark !== isDarkModeRef.current) {
        setIsDarkMode(shouldBeDark);
        isDarkModeRef.current = shouldBeDark;
      }
    }
  }, []);
  
  // Calculate content height function - debounced to prevent excessive calls
  const calculateContentHeight = useCallback(() => {
    if (!contentWrapperRef.current) return;
    
    // Get all the direct children of the content wrapper
    const children = Array.from(contentWrapperRef.current.children);
    
    // Calculate the total height by summing up the heights of all children
    let totalHeight = 0;
    children.forEach(child => {
      totalHeight += child.offsetHeight;
    });
    
    // Add extra space at the end to ensure we can scroll past the last element
    totalHeight += 200;
    
    // Update ref first
    contentHeightRef.current = totalHeight;
    
    // If Lenis is already initialized, update its instance
    if (lenisRef.current) {
      lenisRef.current.resize();
    }
  }, []);
  
  // Initialize Lenis for smooth scrolling
  useEffect(() => {
    // Check if contentRef is available
    if (!contentRef.current || !contentWrapperRef.current) return;
    
    // Initialize Lenis with the same settings as in App.js
    const lenisInstance = new Lenis({
      duration: 3.0, 
      easing: (t) => (t === 1 ? 1 : 1 - Math.pow(2, -10 * t)),
      orientation: 'vertical', 
      gestureOrientation: 'vertical', 
      smoothWheel: true,
      wheelMultiplier: 0.8, 
      smoothTouch: true, 
      touchMultiplier: 1.5, 
      infinite: false,
      wrapper: contentRef.current,
      content: contentWrapperRef.current, // Specify content element
      autoResize: true, // Add auto resize to handle content changes
      normalizeWheel: true // Helps with consistent scrolling behavior
    });
    
    lenisRef.current = lenisInstance;
    
    lenisRef.current.on("scroll", handleScrollWrapper);
    
    // Animation frame loop for Lenis
    function raf(time) {
      lenisRef.current?.raf(time);
      rafRef.current = requestAnimationFrame(raf);
    }
    
    rafRef.current = requestAnimationFrame(raf);
    
    // Reset scroll position to top when projectId changes
    lenisRef.current.scrollTo(0, { immediate: true });
    setScrollProgress(0);
    setIsDarkMode(false);
    
    // Initialize footer opacity CSS custom property
    if (footerContentRef.current) {
      footerContentRef.current.style.setProperty('--footer-opacity', '0');
    }
    
    // Calculate initial height after a short delay to ensure DOM is ready
    setTimeout(calculateContentHeight, 300);

    // Cleanup
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
      
      if (lenisRef.current) {
        lenisRef.current.off("scroll", handleScrollWrapper);
        lenisRef.current.destroy();
        lenisRef.current = null;
      }
      
      // Cleanup mouse interaction timeout
      if (mouseInteractionTimeoutRef.current) {
        clearTimeout(mouseInteractionTimeoutRef.current);
      }
    };
  }, [projectId, calculateContentHeight]); // Only depend on projectId and memoized function

  // Add this useEffect to ensure Lenis knows the correct scrollable area
  useEffect(() => {
    if (lenisRef.current && contentWrapperRef.current) {
      // Force Lenis to recalculate dimensions
      const resizeObserver = new ResizeObserver(() => {
        // When content size changes, recalculate height and update Lenis
        calculateContentHeight();
      });
      
      resizeObserver.observe(contentWrapperRef.current);
      
      return () => {
        if (contentWrapperRef.current) {
          resizeObserver.unobserve(contentWrapperRef.current);
        }
        resizeObserver.disconnect();
      };
    }
  }, [projectId, calculateContentHeight]); // Only depend on projectId and memoized function

  // Add window resize handler to update Lenis
  useEffect(() => {
    const handleResize = () => {
      if (lenisRef.current && contentWrapperRef.current) {
        calculateContentHeight();
      }
    };
    
    const controller = new AbortController();
    window.addEventListener('resize', handleResize, { signal: controller.signal });
    
    return () => {
      controller.abort();
    };
  }, [calculateContentHeight]); // Only depend on memoized function
  
  // Add effect to delay animation start
  useEffect(() => {
    // Reset animation state when project changes
    setStartAnimation(false);
    
    // Reset mouse interaction state
    setIsMouseInteracting(false);
    
    // Cleanup mouse interaction timeout
    if (mouseInteractionTimeoutRef.current) {
      clearTimeout(mouseInteractionTimeoutRef.current);
    }
    
    // Start animation much faster - reduced from 100ms to 50ms
    const animationTimer = setTimeout(() => {
      setStartAnimation(true);
    }, 50);
    
    return () => clearTimeout(animationTimer);
  }, [projectId]);
  
  // Add effect for back button, about button, and hero image fade-in animation
  useEffect(() => {
    // Initially hide the elements
    if (backButtonRef.current) {
      gsap.set(backButtonRef.current, { opacity: 0 });
    }
    
    if (aboutButtonRef.current) {
      gsap.set(aboutButtonRef.current, { opacity: 0 });
    }
    
    if (heroImageRef.current) {
      gsap.set(heroImageRef.current, { opacity: 0 });
    }
    
    // Fade in elements much faster - reduced from 2500ms to 300ms
    const buttonAnimationTimer = setTimeout(() => {
      if (backButtonRef.current) {
        gsap.to(backButtonRef.current, {
          opacity: 1,
          duration: 0.4, // Reduced from 0.8 to 0.4 for faster animation
          ease: "power2.inOut"
        });
      }
      
      if (aboutButtonRef.current) {
        gsap.to(aboutButtonRef.current, {
          opacity: 1,
          duration: 0.4, // Reduced from 0.8 to 0.4 for faster animation
          ease: "power2.inOut"
        });
      }
      
      if (heroImageRef.current) {
        gsap.to(heroImageRef.current, {
          opacity: 1,
          duration: 0.4, // Reduced from 0.8 to 0.4 for faster animation
          ease: "power2.inOut"
        });
      }
    }, 300); // Reduced from 2500ms to 300ms for much faster appearance
    
    return () => clearTimeout(buttonAnimationTimer);
  }, [projectId]); // Re-run when project changes
  
  // Optimized pointer event handler with useCallback
  const handlePointerMove = useCallback((e) => {
    // Only handle mouse interaction if device supports Fluid effect
    if (supportsFluid) {
      // Trigger mouse interaction detection for Fluid effect
      handleMouseInteraction();
      
      const canvasElement = document.querySelector(`.${styles['project-fluid-canvas']} canvas`);
      if (canvasElement) {
        const event = new PointerEvent('pointermove', {
          clientX: e.clientX,
          clientY: e.clientY,
          bubbles: true
        });
        canvasElement.dispatchEvent(event);
      }
    }
  }, [handleMouseInteraction, supportsFluid]);
  
  useEffect(() => {
    if (onReady) {
      const readyTimerId = setTimeout(() => onReady(), 50);
      return () => clearTimeout(readyTimerId);
    }
  }, [projectId, onReady]);
  
  // Function to determine the next project ID
  const getNextProjectId = () => {
    if (!project || !projectsData) return null;
    
    const projectIds = Object.keys(projectsData);
    const currentIndex = projectIds.indexOf(projectId);
    
    // If current project is the last one, go to the first project
    // Otherwise go to the next project in the list
    return currentIndex < projectIds.length - 1 
      ? projectIds[currentIndex + 1] 
      : projectIds[0];
  };
  
  // Optimized handler for next project navigation using custom event
  const handleNextProject = useCallback(() => {
    const nextProjectId = getNextProjectId();
    if (nextProjectId && projectsData[nextProjectId]) {
      // Create and dispatch a custom event that App.js can listen for
      const customEvent = new CustomEvent('navigateToProject', {
        detail: {
          projectId: nextProjectId,
          projectName: projectsData[nextProjectId].name
        }
      });
      window.dispatchEvent(customEvent);
    }
  }, [projectId, projectsData]);
  
  // Memoized background color calculation - OPTIMIZED with better dependencies
  const backgroundColor = useMemo(() => {
    // Only recalculate when scrollProgress changes significantly (every 0.1)
    const roundedProgress = Math.round(scrollProgress * 10) / 10;
    const r = Math.round(232 - (232 * roundedProgress));
    const g = Math.round(232 - (232 * roundedProgress));
    const b = Math.round(232 - (232 * roundedProgress));
    return `rgb(${r}, ${g}, ${b})`;
  }, [Math.round(scrollProgress * 10) / 10]); // Only recalculate on significant changes

  // Memoized fluid color calculation for better performance
  const fluidColor = useMemo(() => {
    return isDarkMode ? '#e8e8e8' : '#3b3b3b';
  }, [isDarkMode]);

  // Extract custom styling from project data or use defaults
  const aboutTitleStyle = project?.aboutTitleStyle || {};
  const descriptionStyle = project?.descriptionStyle || {};
  const infoGridStyle = project?.infoGridStyle || {};

  // Special handling for QUBE project
  const isQubeProject = projectId === 'qube';
  const isNordwoodProject = projectId === 'nordwood';
  
  // Custom grid and heading styles for QUBE project
  const qubeInfoGridStyle = isQubeProject ? {
    gridTemplateColumns: '1fr 1fr', // Make the columns more equal for QUBE project
    ...infoGridStyle
  } : infoGridStyle;
  
  // Custom info heading style for QUBE project - adding 20px top margin to move it down
  const qubeInfoHeadingStyle = isQubeProject ? {
    paddingRight: '20px', // Add more padding to make it visually wider
    maxWidth: '100%', // Allow full width in its column
    width: '100%', // Ensure it takes full width
    marginTop: '20px' // Move the heading 20px lower
  } : {};

  if (!project && projectId) {
    return (
      <div className={styles['project-page-container-wrapper']} style={{ backgroundColor: '#e8e8e8' }}>
        <button 
          onClick={onBack} 
          className={`${styles['back-button']} ${styles['project-page-back-button']}`}
          ref={backButtonRef}
        >
          &larr; Back
        </button>
        <div style={{ color: 'black', textAlign: 'center', paddingTop: '20vh' }}>
          <h1>Project Not Found</h1>
          <p>The project with ID "{projectId}" does not exist.</p>
        </div>
      </div>
    );
  }

  if (!project) {
    return null;
  }

  // Memoized project data to prevent unnecessary recalculations
  const projectAssets = useMemo(() => {
    const heroImage = project.heroImage || '/placeholder.jpg';
    const heroImage2 = project.heroImage2 || heroImage;
    const heroVideo = project.heroVideo || null;
    const contentImages = project.images && project.images.length >= 4 
      ? project.images.slice(0, 4) 
      : [
          { src: heroImage, alt: "Project image 1" },
          { src: heroImage, alt: "Project image 2" },
          { src: heroImage, alt: "Project image 3" },
          { src: heroImage, alt: "Project image 4" }
        ];
    
    return { heroImage, heroImage2, heroVideo, contentImages };
  }, [project]);

  return (
    <div 
      className={`${styles['project-page-container-wrapper']} ${scrollProgress > 0 ? styles['transitioning'] : ''}`} 
      style={{ 
        backgroundColor,
        '--scroll-progress': scrollProgress,
        '--dark-background': 'rgb(0, 0, 0)',
        '--light-background': 'rgb(232, 232, 232)'
      }}
    >
      {/* Back button positioned at top left - with ref for fade-in */}
      <button 
        onClick={onBack} 
        className={`${styles['back-button']} ${styles['project-page-back-button']}`}
        style={{ color: isDarkMode ? 'white' : 'black' }}
        ref={backButtonRef}
      >
        &larr; Back
      </button>
      
      {/* Performance indicator - only show in development or for debugging */}
      {process.env.NODE_ENV === 'development' && devicePerformance && (
        <div 
          style={{ 
            position: 'fixed', 
            top: '20px', 
            right: '20px', 
            background: devicePerformance.tier === 'high' ? 'rgba(0, 255, 0, 0.2)' : 
                       devicePerformance.tier === 'medium' ? 'rgba(255, 165, 0, 0.2)' : 'rgba(255, 0, 0, 0.2)',
            color: isDarkMode ? 'white' : 'black',
            padding: '8px 12px',
            borderRadius: '20px',
            fontSize: '12px',
            fontFamily: 'monospace',
            zIndex: 1000,
            border: `1px solid ${devicePerformance.tier === 'high' ? 'rgba(0, 255, 0, 0.5)' : 
                                   devicePerformance.tier === 'medium' ? 'rgba(255, 165, 0, 0.5)' : 'rgba(255, 0, 0, 0.5)'}`
          }}
        >
          {devicePerformance.tier.toUpperCase()} ({devicePerformance.score}/100)
          {supportsFluid ? ' ✓ Fluid' : ' ✗ Fluid'}
        </div>
      )}
      
      {/* Fluid Canvas for ProjectPage - OPTIMIZED for performance */}
      <div className={styles['project-fluid-canvas']}>
        <Canvas
          camera={{ position: [0, 0, 5], fov: 45 }}
          gl={{ 
            alpha: true, 
            antialias: true, 
            powerPreference: 'high-performance',
            clearColor: new Color(backgroundColor).toArray(),
            pixelRatio: Math.min(window.devicePixelRatio, 2) // Limit high-DPI rendering
          }}
          frameloop={supportsFluid && (scrollProgress <= 0.1 || isMouseInteracting) ? "always" : "demand"} // Only render always on high-performance devices
          style={{ pointerEvents: scrollProgress > 0.1 ? 'none' : 'auto' }}
        >
          <Suspense fallback={null}>
            <color attach="background" args={[backgroundColor]} />
            <ambientLight intensity={0.5} />
            <pointLight position={[5, 5, 5]} intensity={1} />
            
            {/* Only render Fluid effect on supported devices */}
            {supportsFluid ? (
              <>
                <EffectComposer enabled={scrollProgress <= 0.1}>
                  <Fluid 
                    radius={0.36} 
                    curl={3} 
                    swirl={2} 
                    distortion={1.5} 
                    force={1.2} 
                    pressure={0.15} 
                    densityDissipation={0.95} 
                    velocityDissipation={1.00} 
                    intensity={5.5} 
                    rainbow={false} 
                    blend={7} 
                    showBackground={true} 
                    backgroundColor='transparent'
                    fluidColor={fluidColor}
                    enabled={scrollProgress <= 0.1}
                  />
                </EffectComposer>
                <ManualFluidEffect />
              </>
            ) : (
              // Fallback for devices that don't support Fluid - simple gradient background
              <mesh position={[0, 0, -1]}>
                <planeGeometry args={[10, 10]} />
                <meshBasicMaterial 
                  color={backgroundColor}
                  transparent={true}
                  opacity={0.8}
                />
              </mesh>
            )}
          </Suspense>
        </Canvas>
      </div>
      
      {/* Black overlay that fades in over the last image */}
      <div 
        className={styles['black-overlay']} 
        style={{ opacity: scrollProgress }}
      ></div>
      
      {/* Project content area with scrollable content */}
      <div 
        className={styles['project-content-container']} 
        ref={contentRef}
        onPointerMove={handlePointerMove}
      >
        {/* Content wrapper with calculated height */}
        <div 
          ref={contentWrapperRef}
          className={styles['project-content-wrapper']}
          style={{ height: contentHeightRef.current > 0 ? `${contentHeightRef.current}px` : 'auto' }}
        >
          {/* 1. Heading centered in viewport - NOW USING ANIMATEDTEXT WITH DELAY */}
          <div className={`${styles['project-hero-section']} ${styles['fluid-effect-element']}`}>
            {startAnimation ? (
              <AnimatedText 
                effect="randomLetters" 
                threshold={0.2}
                duration={2.5} // Reduced from 5 to 2.5 for faster text animation
                staggerAmount={0.5} // Reduced from 0.7 to 0.3 for faster letter appearance
                outDuration={0.4} // Reduced from 0.7 to 0.4 for faster exit
                outStaggerAmount={0.2} // Reduced from 0.4 to 0.2 for faster exit
                elementType="h1"
                className={styles['project-title']}
                // Use the color from the parent component
                style={{ color: isDarkMode ? 'white' : 'black' }}
              >
                {project.title}
              </AnimatedText>
            ) : (
              // Initial placeholder until animation starts
              <h1 
                className={styles['project-title']} 
                style={{ 
                  color: isDarkMode ? 'white' : 'black',
                  opacity: 0 // Start invisible
                }}
              >
                {project.title}
              </h1>
            )}
          </div>
          
          {/* 2. Big image or video center/center - with ref for fade-in */}
          <div 
            className={`${styles['project-hero-image']} ${styles['fluid-effect-element']}`} 
            ref={heroImageRef}
          >
            {isNordwoodProject ? (
              // For nordwood project, use heroImage2 instead of heroVideo
              <img src={projectAssets.heroImage2} alt={project.title} />
            ) : projectAssets.heroVideo ? (
              // For other projects, use heroVideo if available with LazyVideo component
              <LazyVideo 
                src={projectAssets.heroVideo.src}
                type={projectAssets.heroVideo.type || "video/mp4"}
                autoPlay={projectAssets.heroVideo.autoplay !== false}
                loop={projectAssets.heroVideo.loop !== false}
                muted={projectAssets.heroVideo.muted !== false}
                controls={projectAssets.heroVideo.controls === true}
                className={styles['hero-video']}

              />
            ) : (
              // Default fallback to heroImage
              <img src={projectAssets.heroImage} alt={project.title} />
            )}
          </div>
          
          <div 
            className={`${styles['project-info-grid']} ${styles['fluid-effect-element']} ${project.infoGridClass || ''} ${isQubeProject ? styles['qube-info-grid'] : ''}`}
            style={{ 
              color: isDarkMode ? 'white' : 'black',
              ...qubeInfoGridStyle
            }}
          >
            <div 
              className={styles['project-info-heading']}
              style={qubeInfoHeadingStyle}
            >
              {/* About button - with ref for fade-in */}
              <h2 
                className={project.aboutTitleClass || ''} 
                style={{
                  ...aboutTitleStyle,
                  ...(isQubeProject ? { maxWidth: '100%', width: '100%' } : {})
                }}
                ref={aboutButtonRef}
              >
                {project.aboutTitle || "About this project"}
              </h2>
            </div>
            <div className={styles['project-info-content']}>
              <p 
                className={project.descriptionClass || ''} 
                style={descriptionStyle}
              >
                {project.description}
              </p>
              <p>
                {/*{project.category && <span>{project.category}</span>}*/}
                {/*{project.client && <span> | {project.client}</span>}*/}
                {/*{project.year && <span> | {project.year}</span>}*/}
              </p>
            </div>
          </div>
          
          {/* 4. Four big images below each other */}
          <div className={styles['project-gallery-images']}>
            {projectAssets.contentImages.map((media, index) => (
              <div 
                className={`${styles['project-gallery-image']} ${styles['fluid-effect-element']}`} 
                key={index}
                ref={index === projectAssets.contentImages.length - 1 ? lastProjectImageRef : null}
              >
                {media.type === "video" ? (
                  <LazyVideo 
                    src={media.src}
                    type={media.videoType || "video/mp4"}
                    autoPlay={media.autoplay !== false}
                    loop={media.loop !== false}
                    muted={media.muted !== false}
                    controls={media.controls === true}
                    className={styles['gallery-video']}

                  />
                ) : (
                  <img src={media.src} alt={media.alt || `${project.title} image ${index + 1}`} />
                )}
              </div>
            ))}
          </div>
          
          {/* Combined Next Project + Footer Section (200vh height) */}
          <div
            ref={footerSectionRef}
            className={styles['combined-footer-section']}
            style={{
              backgroundColor: 'transparent',
              height: '200vh',
              position: 'relative',
              zIndex: 4
            }}
          >
            {/* Next Project Section - First 100vh */}
            <div 
              className={styles['next-project-section']}
              style={{ 
                height: '100vh',
                backgroundColor: 'transparent',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column'
              }}
            >
              <button 
                onClick={handleNextProject} 
                className={styles['next-project-button']}
                style={{ color: 'white' }}
              >
                See next Project
              </button>
            </div>

            {/* Footer Content Section - Second 100vh */}
            <div 
              ref={footerContentRef}
              className={styles['project-footer-content']}
            >
              {/* Big Contact Email Heading in Center */}
              <div className={styles['contact-email-container']}>
                <a href="mailto:<EMAIL>" className={styles['contact-email-link']}>
                  <h2 className={styles['contact-email-heading']}><EMAIL></h2>
                </a>
              </div>
              
              {/* Bottom Bar with Copyright and Built By */}
              <div className={styles['contact-bottom']}>
                <div className={styles['contact-copyright']}>All rights reserved. © 2025 BLCKs</div>
                <div className={styles['contact-built-by']}>built by BLCKs with ❤</div>

                {/* Footer section with 3 elements positioned as requested */}
                <div className={styles['footer-section']}>
                  {/* Footer links on the left */}
                  <div className={styles['footer-links-container']}>
                    <div className={styles['footer-links']}>
                      <a href="https://aback-syrup-e00.notion.site/BLCKS-IMPRESSUM-20d5934475c580629a78fdc6bbee5d60" className={styles['footer-link']} target="_blank" rel="noopener noreferrer">Impressum</a>
                      <a href="https://aback-syrup-e00.notion.site/BLCKS-DATENSCHUTZERKL-RUNG-20d5934475c580b69fdae48e3a5c6848" className={styles['footer-link']} target="_blank" rel="noopener noreferrer">Datenschutz</a>
                      <a href="https://aback-syrup-e00.notion.site/BLCKS-COOKIE-RICHTLINIE-20d5934475c58034a1edc5c56179f398" className={styles['footer-link']} target="_blank" rel="noopener noreferrer">Cookies</a>
                    </div>
                  </div>
                  
                  {/* Social icons in the middle */}
                  <div className={styles['social-icons']}>
                    <a href="#" className={styles['social-icon']}>
                      <img src="/instagram.svg" alt="Instagram" />
                    </a>
                    <a href="https://www.linkedin.com/in/philippfuchs-blcks" className={styles['social-icon']} target="_blank" rel="noopener noreferrer">
                      <img src="/linkedin.svg" alt="LinkedIn" />
                    </a>
                    <a href="#" className={styles['social-icon']}>
                      <img src="/twitter.svg" alt="Twitter" />
                    </a>
                  </div>

                  {/* QR Code section on the right */}
                  <div className={styles['qr-code-container']}>
                    <img src="/qr-code.svg" alt="WhatsApp QR Code" className={styles['qr-code-image']} />
                    <h3 className={styles['qr-code-heading']}>WhatsApp Coming</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      

    </div>
  );
}

export default ProjectPage;