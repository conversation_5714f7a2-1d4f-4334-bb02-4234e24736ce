import React, { useState, useEffect, useRef } from 'react';
import lottie from 'lottie-web';
import styles from './NewPreloader.module.css';

const NewPreloader = ({ onComplete }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showSplitScreen, setShowSplitScreen] = useState(false);
  const lottieContainer = useRef(null);

  useEffect(() => {
    let animation;
    
    fetch('/lottie/BLCKS_PRELOADER_FINAL.json')
      .then(response => response.json())
      .then(animationData => {
        // Clear any existing content first
        if (lottieContainer.current) {
          lottieContainer.current.innerHTML = '';
        }
        
        animation = lottie.loadAnimation({
          container: lottieContainer.current,
          renderer: 'svg',
          loop: true,
          autoplay: true,
          animationData: animationData
        });
      })
      .catch(error => {
        console.error('Error loading Lottie animation:', error);
      });

    return () => {
      if (animation) {
        animation.destroy();
      }
    };
  }, []);

  const handleEnterClick = () => {
    // Clear the Lottie animation when switching to split screen
    if (lottieContainer.current) {
      lottieContainer.current.innerHTML = '';
    }
    setShowSplitScreen(true);
  };

  const handleLeftSideClick = () => {
    if (onComplete) {
      onComplete('left');
    }
  };

  const handleRightSideClick = () => {
    if (onComplete) {
      onComplete('right');
    }
  };

  if (showSplitScreen) {
    return (
      <div className={styles.splitScreenWrapper}>
        <div 
          className={styles.leftSide}
          onClick={handleLeftSideClick}
        >
          <div className={styles.sideContent}>
            <h2 className={styles.sideTitle}>BLCKS.DESIGN</h2>
            <p className={styles.sideDescription}>Design, Websites & <br /> digitale Erlebnisse.</p>
            <button 
              className={styles.sideButton}
              onClick={(e) => {
                e.stopPropagation();
                handleLeftSideClick();
              }}
            >
              [ ENTER ]
            </button>
          </div>
        </div>
        <div 
          className={styles.rightSide}
          onClick={handleRightSideClick}
        >
          <div className={styles.sideContent}>
            <h2 className={styles.sideTitle}>BLCKS.AI</h2>
            <p className={styles.sideDescription}>KI - Automatisierungen & <br /> digitale Lösungen.</p>
            <button 
              className={styles.sideButton}
              onClick={(e) => {
                e.stopPropagation();
                handleRightSideClick();
              }}
            >
              [ ENTER ]
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.preloaderWrapper}>
      <div className={styles.preloaderContent}>
        <div 
          ref={lottieContainer}
          className={styles.lottieAnimation}
        ></div>
        <button
          className={`${styles.enterButton} ${isHovered ? styles.hovered : ''}`}
          onClick={handleEnterClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          [ ENTER ]
        </button>
      </div>
    </div>
  );
};

export default NewPreloader;