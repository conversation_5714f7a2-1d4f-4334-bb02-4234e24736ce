import React, { useState, useEffect, useRef } from 'react';
import styles from './PerformanceMonitor.module.css';

const PerformanceMonitor = ({ 
  show = true, 
  position = 'top-right',
  theme = 'dark',
  stats = ['fps', 'ms', 'mb', 'custom'],
  onReport = null,
  reportInterval = 1000
}) => {
  const [metrics, setMetrics] = useState({
    fps: 0,
    ms: 0,
    mb: 0,
    custom: 0,
  });
  
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const frameTimes = useRef([]);
  const rafId = useRef();
  const memory = useRef({ usedJSHeapSize: 0, totalJSHeapSize: 0 });
  const lastReportTime = useRef(0);

  // Measure FPS and frame time
  const measurePerformance = (now) => {
    frameCount.current++;
    
    // Calculate FPS and frame time
    const delta = now - lastTime.current;
    if (delta >= 1000) {
      const fps = Math.round((frameCount.current * 1000) / delta);
      
      // Calculate average frame time
      const frameTime = delta / frameCount.current;
      
      // Get memory usage if available
      if (window.performance && window.performance.memory) {
        memory.current = {
          usedJSHeapSize: window.performance.memory.usedJSHeapSize,
          totalJSHeapSize: window.performance.memory.totalJSHeapSize,
        };
      }
      
      // Calculate memory in MB
      const usedMB = memory.current.usedJSHeapSize / (1024 * 1024);
      
      // Update metrics
      const newMetrics = {
        fps,
        ms: frameTime.toFixed(1),
        mb: usedMB.toFixed(2),
        custom: 0, // Can be used for custom metrics
      };
      
      setMetrics(newMetrics);
      
      // Reset counters
      frameCount.current = 0;
      lastTime.current = now;
      
      // Report metrics if callback provided
      if (onReport && now - lastReportTime.current >= reportInterval) {
        onReport(newMetrics);
        lastReportTime.current = now;
      }
    }
    
    // Continue the measurement loop
    rafId.current = requestAnimationFrame(measurePerformance);
  };

  // Start/stop measurement based on show prop
  useEffect(() => {
    if (show) {
      lastTime.current = performance.now();
      rafId.current = requestAnimationFrame(measurePerformance);
    }
    
    return () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, [show]);

  // Add custom metric
  const setCustomMetric = (value) => {
    setMetrics(prev => ({
      ...prev,
      custom: typeof value === 'number' ? value.toFixed(2) : value
    }));
  };

  // Don't render anything if hidden
  if (!show) return null;

  // Position classes
  const positionClasses = {
    'top-left': styles.topLeft,
    'top-right': styles.topRight,
    'bottom-left': styles.bottomLeft,
    'bottom-right': styles.bottomRight,
  }[position] || styles.topRight;

  // Theme classes
  const themeClasses = {
    'light': styles.light,
    'dark': styles.dark,
  }[theme] || styles.dark;

  return (
    <div className={`${styles.performanceMonitor} ${positionClasses} ${themeClasses}`}>
      <div className={styles.metrics}>
        {stats.includes('fps') && (
          <div className={styles.metric}>
            <span className={styles.label}>FPS:</span>
            <span className={`${styles.value} ${metrics.fps < 30 ? styles.warning : ''} ${metrics.fps < 20 ? styles.critical : ''}`}>
              {metrics.fps}
            </span>
          </div>
        )}
        
        {stats.includes('ms') && (
          <div className={styles.metric}>
            <span className={styles.label}>MS:</span>
            <span className={styles.value}>{metrics.ms}</span>
          </div>
        )}
        
        {stats.includes('mb') && (
          <div className={styles.metric}>
            <span className={styles.label}>MEM:</span>
            <span className={styles.value}>{metrics.mb} MB</span>
          </div>
        )}
        
        {stats.includes('custom') && stats.length > 1 && (
          <div className={styles.metric}>
            <span className={styles.label}>CUSTOM:</span>
            <span className={styles.value}>{metrics.custom}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitor;

export const usePerformanceMonitor = (options = {}) => {
  const [metrics, setMetrics] = React.useState({});
  const [show, setShow] = React.useState(options.show !== false);
  
  const toggle = () => setShow(prev => !prev);
  
  const monitor = (
    <PerformanceMonitor 
      {...options} 
      show={show} 
      onReport={options.onReport || setMetrics} 
    />
  );
  
  return [monitor, metrics, { toggle, show, setShow }];
};
