import { StrictMode, useState } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App3 from './App3Optimized.jsx'
import AppAI from './AppAIPlaceholder.jsx'
import NewPreloader from './NewPreloader.jsx'

function AppSelector() {
  const [selectedApp, setSelectedApp] = useState(null);

  const handlePreloaderComplete = (side) => {
    if (side === 'left') {
      setSelectedApp('app3'); // Main website (App3.jsx)
    } else if (side === 'right') {
      setSelectedApp('appAI'); // Alternative version (App2.jsx)
    }
  };

  if (!selectedApp) {
    return <NewPreloader onComplete={handlePreloaderComplete} />;
  }

  if (selectedApp === 'app3') {
    return <App3 onBack={() => setSelectedApp(null)} />;
  }

  if (selectedApp === 'appAI') {
    return <AppAI onBack={() => setSelectedApp(null)} />;
  }

  return null;
}

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <AppSelector />
  </StrictMode>,
)
