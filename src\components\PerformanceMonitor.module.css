.performanceMonitor {
  position: fixed;
  z-index: 10000;
  padding: 8px 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  border-radius: 4px;
  pointer-events: none;
  user-select: none;
  opacity: 0.9;
  transition: opacity 0.2s;
}

.performanceMonitor:hover {
  opacity: 1;
}

/* Position classes */
.topRight {
  top: 10px;
  right: 10px;
}

.topLeft {
  top: 10px;
  left: 10px;
}

.bottomRight {
  bottom: 10px;
  right: 10px;
}

.bottomLeft {
  bottom: 10px;
  left: 10px;
}

/* Theme classes */
.dark {
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}

.light {
  background-color: rgba(255, 255, 255, 0.8);
  color: #000;
  text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.5);
}

.metrics {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric {
  display: flex;
  justify-content: space-between;
  min-width: 100px;
}

.label {
  margin-right: 10px;
  font-weight: bold;
  opacity: 0.8;
}

.value {
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

.warning {
  color: #ffcc00;
}

.critical {
  color: #ff3333;
  font-weight: bold;
}
