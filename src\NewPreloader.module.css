/* Font faces for Inter */
@font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Thin.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-ExtraLight.woff2') format('woff2');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-SemiBold.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-ExtraBold.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Black.woff2') format('woff2');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
  }
  
  /* Font faces for Supply */
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-UltraLight.otf') format('opentype');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-Light.otf') format('opentype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-Regular.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-Medium.otf') format('opentype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-Bold.otf') format('opentype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  } 

  @font-face {
    font-family: 'Archivo';
    src: url('/fonts/archivo-black-regular') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  } 


.preloaderWrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
}

.preloaderContent {
  display: flex;
  align-items: center;
  justify-content: center;
}

.enterButton {
  background: transparent;
  border: 0px solid #ffffff;
  color: #ffffff;
  padding: 10px 40px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-family: Inter, sans-serif;
  position: relative;
  overflow: hidden;
}

.enterButton:hover,
.enterButton.hovered {
  transform: scale(1.05);
}

.enterButton:active {
  transform: scale(1);
}

/* Responsive design */
@media (max-width: 768px) {
  .enterButton {
    padding: 16px 32px;
    font-size: 16px;
    letter-spacing: 1.5px;
  }
}

.preloaderContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .lottieAnimation {
    width: 850px;
    height: 150px;
    margin-bottom: 0px;
  }
  
  /* Responsive design for mobile */
  @media (max-width: 768px) {
    .lottieAnimation {
      width: 300px;
      height: 300px;
      margin-bottom: 30px;
    }
  }

/* Split Screen Overlay Styles */
.splitScreenWrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  z-index: 99999;
}

.leftSide {
  flex: 1;
  background-color: #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.leftSide:hover {
  background-color: #ffffff;
}

.rightSide {
  flex: 1;
  background-color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.rightSide:hover {
  background-color: #1a1a1a;
}

.sideContent {
  text-align: center;
  padding: 40px;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.sideTitle {
  font-family: 'Archivo', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  letter-spacing: -0.02em;
}

.leftSide .sideTitle {
  color: #000000;
}

.rightSide .sideTitle {
  color: #ffffff;
}

.sideDescription {
  font-family: 'Supply', sans-serif;
  font-size: 1.0rem;
  line-height: 1.6;
  margin-bottom: 40px;
  font-weight: 400;
  text-transform: uppercase;
}

.leftSide .sideDescription {
  color: #333333;
}

.rightSide .sideDescription {
  color: #cccccc;
}

.sideButton {
  background: transparent;
  border: 0px solid;
  padding: 15px 40px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  font-family: 'Inter', sans-serif;
  position: relative;
  overflow: hidden;
}

.leftSide .sideButton {
  border-color: #000000;
  color: #000000;
}



.rightSide .sideButton {
  border-color: #ffffff;
  color: #ffffff;
}




/* Responsive design for split screen */
@media (max-width: 768px) {
  .splitScreenWrapper {
    flex-direction: column;
  }
  
  .sideContent {
    padding: 30px 20px;
    max-width: 300px;
  }
  
  .sideTitle {
    font-size: 2rem;
  }
  
  .sideDescription {
    font-size: 1rem;
  }
  
  .sideButton {
    padding: 12px 30px;
    font-size: 14px;
  }
}