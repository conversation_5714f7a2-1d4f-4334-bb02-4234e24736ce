/* Font faces for Inter */
@font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Thin.woff2') format('woff2');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-ExtraLight.woff2') format('woff2');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Medium.woff2') format('woff2');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-SemiBold.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Bold.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-ExtraBold.woff2') format('woff2');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Inter';
    src: url('/fonts/Inter-Black.woff2') format('woff2');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
  }
  
  /* Font faces for Supply */
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-UltraLight.otf') format('opentype');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-Light.otf') format('opentype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-Regular.otf') format('opentype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-Medium.otf') format('opentype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }
  @font-face {
    font-family: 'Supply';
    src: url('/fonts/Supply-Bold.otf') format('opentype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  } 

  @font-face {
    font-family: 'Archivo';
    src: url('/fonts/archivo-black-regular') format('woff2');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  } 





.app-ai-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #000000;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
  pointer-events: auto;
}

.background-container canvas {
  width: 100vw !important;
  height: 100vh !important;
  display: block;
  pointer-events: auto;
}

.back-button {
  position: absolute;
  top: 2rem;
  left: 2rem;
  border: 0px solid #ffffff;
  color: #ffffff;
  background: transparent;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  letter-spacing: 0.05em;
  cursor: pointer;
  z-index: 1000;
  pointer-events: auto;
}

.back-button:hover {
    background-color: #000000;
}

.center-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 10;
  pointer-events: none;
}

.center-content h1,
.center-content p,
.center-content a {
  pointer-events: auto;
}

.main-title {
  font-size: 4rem;
  font-family: 'Archivo', sans-serif;
  font-weight: 600;
  margin: 0 0 1rem 0;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #ffffff 0%, #cccccc 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  font-family: 'Supply', sans-serif;
  margin: 0 0 2rem 0;
  color: #cccccc;
  letter-spacing: 0.02em;
}

.description {
  font-size: 1rem;
  font-weight: 300;
  line-height: 1.6;
  margin-top: 16px;
  color: #999999;
  max-width: 600px;
}

.email-button {
  display: inline-block;
  margin-top: 3rem;
  padding: 1rem 2rem;
  background: transparent;
  border: 0px solid #ffffff;
  color: #ffffff;
  text-decoration: none;
  font-family: 'Inter', sans-serif;
  font-size: 1.5rem;
  font-weight: 500;
  letter-spacing: 2px;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.email-button:hover {
  background-color: #000000;
  color: #ffffff;
  transform: scale(1.05);
}

/* Responsive design */
@media (max-width: 768px) {
  .back-button {
    top: 1.5rem;
    left: 1.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
  
  .main-title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.25rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .email-button {
    font-size: 1rem;
    padding: 0.875rem 1.75rem;
    margin-top: 1.5rem;
  }
  
  .center-content {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.125rem;
  }
  
  .description {
    font-size: 0.875rem;
  }
  
  .email-button {
    font-size: 0.875rem;
    padding: 0.75rem 1.5rem;
    margin-top: 1.25rem;
  }
}
