import { createPortal, use<PERSON>rame, useThree } from "@react-three/fiber";
import { useEffect, useMemo, useRef, useState } from "react";
import * as THREE from "three";

import { Cameras, useCameraStore, mainCamera, orbitCamera } from "./cameras_production";
import { FLOW_SIM_SIZE, RAYMARCH_WATER_CENTER, RAYMARCH_FLOW_SIZE } from "./scene_copy/constants2";
import { Env } from "./scene_copy/env2";
import { renderFlow } from "./scene_copy/render-flow2";
import { useAssets } from "./scene_copy/use-assets2";
import { useLerpMouse } from "./scene_copy/use-lerp-mouse2";
import { useMaterials } from "./scene_copy/use-materials2";
import { useTargets } from "./scene_copy/use-targets2";

export function Scene({ opacity = 1, position = [0,0,0], ripples = [] }) {
  const activeCamera = useCameraStore((state) => state.camera);

  // Production settings - no controls, no wireframe
  const debugFloor = false;
  const renderFloor = true;
  const debugTextures = false;
  const showFloorWireframe = false;
  const wireframeColor = '#00ff00';
  const wireframeOpacity = 1.0;
  const showCornerMarkers = false;

  const targets = useTargets();
  const { flowFbo, orbeFlowFbo } = targets;
  const assets = useAssets();
  const materials = useMaterials(targets, assets);
  const { flowMaterial, raymarchMaterial, updateFlowCamera } = materials;

  updateFlowCamera(activeCamera);

  const [envMap, setEnvMap] = useState(null);

  // update environment
  useFrame(({ scene }) => {
    const env = scene.environment;
    if (!env) return;
    const currentEnv = raymarchMaterial.uniforms.envMap.value;
    if (currentEnv !== env) {
      raymarchMaterial.uniforms.envMap.value = env;
      const rotation = raymarchMaterial.uniforms.envMapRotation.value;

      const _e1 = new THREE.Euler();
      const _m1 = new THREE.Matrix4();

      _e1.copy(scene.environmentRotation);

      // accommodate left-handed frame
      _e1.x *= -1;
      _e1.y *= -1;
      _e1.z *= -1;

      _e1.y *= -1;
      _e1.z *= -1;

      rotation.setFromMatrix4(_m1.makeRotationFromEuler(_e1));

      setEnvMap(env);
    }
  });

  const [handlePointerMoveFloor, lerpMouseFloor, vRefsFloor] = useLerpMouse();

  const frameCount = useRef(0);

  const screenFbo = useMemo(() => {
    const fbo = new THREE.WebGLRenderTarget(10, 10, {
      depthBuffer: true,
      depthTexture: new THREE.DepthTexture(10, 10)
    });

    if (typeof window !== "undefined") {
      fbo.setSize(window.innerWidth, window.innerHeight);
    }

    return fbo;
  }, []);

  const size = useThree((state) => state.size);

  useEffect(() => {
    screenFbo.setSize(size.width, size.height);
  }, [size, screenFbo]);

  useEffect(() => {
    // for dev reasons, reset frame count when material gets reloaded
    frameCount.current = 0;
  }, [flowMaterial, flowFbo]);

  const flowScene = useMemo(() => new THREE.Scene(), []);

  // Update flow simulation
  useFrame(({ gl, scene, clock }, delta) => {
    const t = clock.getElapsedTime();

    let s = Math.sin(t * 1) - 0.2;
    if (s < 0) {
      s = 0;
    } else {
      s = 1;
    }

    let triangleHeight = s * 0.4 + 0.5;

    flowMaterial.uniforms.uTriangleHeight.value = triangleHeight;

    if (geoTryRef.current) {
      geoTryRef.current.scale.y = (triangleHeight - 0.5) * 3;
    }

    const shouldDoubleRender = delta > 1 / 75;

    gl.setRenderTarget(null);

    // floor
    lerpMouseFloor(shouldDoubleRender ? delta / 2 : delta);
    renderFlow(
      gl,
      activeCamera,
      flowScene,
      clock,
      flowMaterial,
      flowFbo,
      vRefsFloor,
      frameCount.current
    );

    if (shouldDoubleRender) {
      // floor
      lerpMouseFloor(delta / 2);
      renderFlow(
        gl,
        activeCamera,
        flowScene,
        clock,
        flowMaterial,
        flowFbo,
        vRefsFloor,
        frameCount.current
      );
    }

    raymarchMaterial.uniforms.uFlowSize.value = FLOW_SIM_SIZE / 2;

    // --- Ripple uniform update ---
    if (!raymarchMaterial.uniforms.ripplePositions) {
      raymarchMaterial.uniforms.ripplePositions = { value: Array(5).fill().map(() => new THREE.Vector2(0, 0)) };
    }
    if (!raymarchMaterial.uniforms.rippleTimes) {
      raymarchMaterial.uniforms.rippleTimes = { value: Array(5).fill(0) };
    }
    // Prepare up to 5 ripples
    const now = performance.now();
    const ripplePositions = Array(5).fill().map(() => new THREE.Vector2(0, 0));
    const rippleTimes = Array(5).fill(0);
    ripples.slice(-5).forEach((r, i) => {
      ripplePositions[i].set(r.x, r.z);
      rippleTimes[i] = (now - r.startTime) * 0.001; // seconds since triggered
    });
    raymarchMaterial.uniforms.ripplePositions.value = ripplePositions;
    raymarchMaterial.uniforms.rippleTimes.value = rippleTimes;

    gl.render(scene, activeCamera);
    frameCount.current++;
  }, 1);

  const geoTryRef = useRef(null);

  useEffect(() => {
    // Set a custom uniform to indicate camera mode
    if (flowMaterial.uniforms.uCameraMode === undefined) {
      flowMaterial.uniforms.uCameraMode = { value: 0 };
    }
    // 0 = main, 1 = orbit
    flowMaterial.uniforms.uCameraMode.value = (activeCamera === orbitCamera) ? 1 : 0;
  }, [activeCamera, flowMaterial]);

  return (
    <group position={position}>
      {/* Flow simulation (floor) */}
      {createPortal(
        <mesh>
          {/* Has to be 2x2 to fill the screen using pos attr */}
          <planeGeometry args={[2, 2]} />
          <primitive object={flowMaterial} />
        </mesh>,
        flowScene
      )}

      {/* Pointer events (floor) */}
      <mesh
        visible={debugFloor}
        rotation={[Math.PI / -2, 0, 0]}
        position={[0, 0, 0]}
        onPointerMove={handlePointerMoveFloor}
        onPointerOver={() => (vRefsFloor.shouldReset = true)}
      >
        <planeGeometry args={[FLOW_SIM_SIZE, FLOW_SIM_SIZE]} />
        <meshBasicMaterial map={flowFbo.read.texture} />
      </mesh>

      {/* Raymarched water (floor) */}
      <mesh
        rotation={[Math.PI / -2, 0, 0]}
        visible={renderFloor}
        position={RAYMARCH_WATER_CENTER}
        onPointerMove={handlePointerMoveFloor}
        onPointerOver={() => (vRefsFloor.shouldReset = true)}
      >
        <planeGeometry args={[RAYMARCH_FLOW_SIZE * 2, RAYMARCH_FLOW_SIZE]} />
        <primitive object={raymarchMaterial} />
      </mesh>

      {/* No wireframe or debug elements for production */}

      <Cameras />
      <Env />
    </group>
  );
}
