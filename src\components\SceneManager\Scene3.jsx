import React, { useRef, useState, useEffect, useCallback } from 'react';
import { useThree } from '@react-three/fiber';
import * as THREE from 'three';
import WaterMatcapFloor from '../WaterMatcap/WaterMatcapFloor';
import { Cameras, useCameraStore } from '../WaterMatcap/scene_floor/cameras3';
import { useControls } from 'leva';

// BlackBackground component (from Scene1)
function BlackBackground() {
  const { scene } = useThree();
  React.useEffect(() => {
    scene.background = new THREE.Color('#000000');
    return () => {
      scene.background = null;
    };
  }, [scene]);
  return null;
}

// Scene3 Component - Simple fullscreen scene with only WaterMatcapFloor
export default function Scene3({ isActive = false, scrollProgress = 0, globalScrollProgress = 0 }) {

  const setOrbitPosition = useCameraStore((state) => state.setOrbitPosition);
  const setOrbitRotation = useCameraStore((state) => state.setOrbitRotation);

  // Default camera position for Scene3 (same as Scene1)
  const defaultPosition = { x: 0, y: 0.35, z: 2 };
  const defaultRotation = { x: -0.12, y: 0, z: 0 };

  // Ripples state for water floor animation
  const [ripples, setRipples] = useState([]);
  const lastRandomRippleTime = useRef(0);

  // Controls for debugging
  const { 
    showDebugInfo,
    enableRandomRipples,
    randomRippleMinInterval,
    randomRippleMaxInterval,
    showGrid,
    showAxes,
    gridSize
  } = useControls('Scene3 Controls', {
    showDebugInfo: false,
    enableRandomRipples: true,
    randomRippleMinInterval: { value: 2, min: 0.5, max: 5, step: 0.1 },
    randomRippleMaxInterval: { value: 4, min: 1, max: 10, step: 0.1 },
    showGrid: false,
    showAxes: false,
    gridSize: { value: 10, min: 5, max: 50, step: 5 }
  });

  // Debug info display
  useEffect(() => {
    if (showDebugInfo) {
      console.log('Scene3 Debug:', {
        isActive,
        scrollProgress: scrollProgress.toFixed(3),
        globalScrollProgress: globalScrollProgress.toFixed(3)
      });
    }
  }, [isActive, scrollProgress, globalScrollProgress, showDebugInfo]);

  // Set camera to default position when Scene3 is active
  useEffect(() => {
    if (isActive) {
      setOrbitPosition(defaultPosition);
      setOrbitRotation(defaultRotation);
    }
  }, [isActive, setOrbitPosition, setOrbitRotation]);

  // Random ambient ripples for water floor animation
  useEffect(() => {
    if (!isActive || !enableRandomRipples) return;

    const interval = setInterval(() => {
      const now = Date.now();
      const timeSinceLastRandom = now - lastRandomRippleTime.current;
      const intervalRange = (randomRippleMaxInterval - randomRippleMinInterval) * 1000;
      const nextRandomInterval = randomRippleMinInterval * 1000 + Math.random() * intervalRange;

      // Count current small ripples (max 3 small ripples at once)
      const currentSmallRipples = ripples.filter(r =>
        r.type === 'small' && (now - r.startTime) < 3000
      ).length;

      if (timeSinceLastRandom > nextRandomInterval && currentSmallRipples < 3) {
        // Create a small random ripple in a random area
        const angle = Math.random() * Math.PI * 2;
        const radius = Math.random() * 3; // Random radius within 3 units

        const x = Math.cos(angle) * radius;
        const z = Math.sin(angle) * radius;

        setRipples(prev => [...prev, {
          x: x,
          z: z,
          startTime: now,
          type: 'small'
        }].slice(-5)); // Keep max 5 ripples

        lastRandomRippleTime.current = now;
      }

      // Clean up old ripples (older than 7 seconds to match shader duration)
      setRipples(prev => prev.filter(ripple => (now - ripple.startTime) < 7000));
    }, 100); // Check every 100ms

    return () => clearInterval(interval);
  }, [isActive, enableRandomRipples, randomRippleMinInterval, randomRippleMaxInterval, ripples]);

  return (
    <group>
      {/* BlackBackground from Scene1 */}
      <BlackBackground />

      {/* Camera system from scene_floor */}
      <Cameras />

      {/* Helper Grid */}
      {showGrid && (
        <gridHelper args={[gridSize, 10, '#888888', '#444444']} />
      )}

      {/* Axes Helper */}
      {showAxes && (
        <axesHelper args={[gridSize / 2]} />
      )}

      {/* Lighting (same as Scene1) */}
      <ambientLight intensity={0.5} />
      <pointLight position={[5, 5, 5]} intensity={1} />

      {/* WaterMatcap Floor with ripples - ONLY component in Scene3 */}
      <WaterMatcapFloor position={[0, 0, 0]} ripples={ripples} />
    </group>
  );
}
