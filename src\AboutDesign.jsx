// AboutDesign.js - WITH 3D BACKGROUND
import React, { useEffect } from 'react';
import { Canvas, useThree, extend } from '@react-three/fiber';
import { OrbitControls, ScrollControls, useScroll, Html } from '@react-three/drei';
import * as THREE from 'three';
import Model from './Aboutblockscene'; // Import your 3D model component
import './AboutDesign.module.css';
import NoiseFilter from './components/NoiseFilter.jsx';



const OverlayNoiseFilter = React.memo(() => {
  const { camera } = useThree();
  useEffect(() => { camera.layers.enable(31); }, [camera]);
  return <NoiseFilter intensity={0.05} speed={0.8} />;
});

// Component that handles scroll-synchronized 3D scene
const ScrollSynchronized3DScene = () => {
  const scroll = useScroll();

  return (
    <>
      <directionalLight position={[0, 0, 5]} intensity={0.25} />

      {/* Very subtle fog for depth effect */}
      <fog attach="fog" args={['#000000', 11, 15]} />

      <Model
        position={[0, 0, 0]}
        scale={[0.75, 0.75, 0.75]}
        scrollProgress={scroll.offset}
      />
      <OverlayNoiseFilter />
      <OrbitControls
        enabled={false} // Disable controls since we want it as background
        enableZoom={false}
        enablePan={false}
        enableRotate={false}
      />
    </>
  );
};

// HTML Content component that works with ScrollControls
const ScrollSynchronizedContent = () => {
  return (
    <Html
      fullscreen
      style={{
        pointerEvents: 'none',
      }}
    >
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100vw',
          zIndex: 100,
          pointerEvents: 'none',
        }}
      >
      {/* Section 1 - 100vh */}
      <div style={{ height: '100vh', display: 'flex', alignItems: 'center', paddingLeft: '10vw' }}>
        <div style={{
          color: 'white',
          maxWidth: '1000px',
          pointerEvents: 'none',
          userSelect: 'none',
          padding: '2rem',
          borderRadius: '10px'
        }}>
            <h1 style={{
              fontSize: '4rem',
              fontWeight: '600',
              margin: '0 0rem 0rem 0',
              color: '#ffffff',
              fontFamily: 'Archivo, sans-serif',
              flex: '1'
            }}>
              BLCKS.
            </h1>
            <h2 style={{
              fontSize: '3rem',
              fontWeight: '600',
              margin: '0 0rem 3rem 0',
              color: '#ffffff',
              fontFamily: 'Archivo, sans-serif',
              flex: '1'
            }}>
              DESIGN STUDIO
            </h2>
          <p style={{
            fontSize: '1.2rem',
            fontWeight: '400',
            lineHeight: '1.5',
            margin: '0',
            color: '#cccccc',
            maxWidth: '750px',
            padding: '0.5rem',
            fontFamily: 'Telegraf, sans-serif'
          }}>
            Bei BLCKS folgen wir nicht einfach Trends - wir setzen auf eine neue Art der digitalen Transformation.
            Eine, die sich an dir, deiner Marke und deiner Zielgruppe orientiert, um nachhaltige, personalisierte Erlebnisse zu schaffen.
          </p>
        </div>
      </div>

      {/* Spacer 75vh */}
      <div style={{ height: '75vh' }} />

      {/* Section 2 - Services */}
      <div style={{ height: '150vh', display: 'flex', alignItems: 'center', paddingLeft: '10vw' }}>
        <div style={{
          color: 'white',
          maxWidth: '1400px',
          pointerEvents: 'none',
          userSelect: 'none',
          padding: '2rem',
          borderRadius: '10px'
        }}>
          <h1 style={{
            fontSize: '1.5rem',
            fontWeight: '400',
            margin: '0 0 8rem 0',
            color: '#cccccc',
            fontFamily: 'Telegraf, sans-serif'
          }}>
            SERVICES
          </h1>

          {[
            { title: "WEBENTWICKLUNG", subtitle: "Visuell starke Webseiten und performante Frontend-Lösungen, die auf jedem Gerät überzeugen.", description: "Responsive Design, Frontend Development, CMS-Integration, Performance Optimierung" },
            { title: "UI/UX-DESIGN", subtitle: "Nutzerzentrierte, intuitive Interfaces gestalten, die Erlebnis und Funktion perfekt vereinen.", description: "User Journeys, Wireframes & Prototypen, Design Systeme, Usability Testing" },
            { title: "3D-ERLEBNISSE", subtitle: "Interaktive 3D-Websites und immersive Experiences, die aus Besuchern echte Entdecker machen.", description: "WebGL, ThreeJS, React Three Fiber, Interaktive 3D Elemente, Produktvisualisierungen, Immersive Storytelling" },
            { title: "E-COMMERCE", subtitle: "Individuelle Online-Shops mit Shopify & Co., die Marken stärken und Verkäufe skalieren.", description: "Shopify Development, Conversion Optimierung, Payment & Checkout Flows" },
            { title: "APP-ENTWICKLUNG", subtitle: "Zuverlässige Apps für iOS & Android, die durch Design, Performance und Usability begeistern.", description: "iOS & Android, Cross-Plattform, Backend-Anbindung, Push Notifications" },
            { title: "SEO & WARTUNG", subtitle: "Technisch saubere Optimierung, stabile Performance und dauerhafte Betreuung für nachhaltigen Erfolg.", description: "Technisches SEO, Performance Monitoring, Support, Security & Updates" }
          ].map((service, index) => (
            <div key={index} style={{ marginBottom: '4rem', display: 'flex', alignItems: 'center' }}>
              <div style={{ flex: '2', marginRight: '25rem' }}>
                <h3 style={{
                  fontSize: '3rem',
                  fontWeight: '800',
                  margin: '0 0 1.5rem 0',
                  color: '#ffffff',
                  fontFamily: 'Telegraf, sans-serif',
                  lineHeight: '1.1',
                  maxWidth: '700px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}>
                  {service.title}
                </h3>
                <h4 style={{
                  fontSize: '1.1rem',
                  fontWeight: '400',
                  margin: '0 0 2rem 0',
                  color: '#cccccc',
                  fontFamily: 'Telegraf, sans-serif',
                  lineHeight: '1.4',
                  opacity: '1',
                  maxWidth: '500px'
                }}>
                  {service.subtitle}
                </h4>
              </div>
              <div style={{ flex: '1', minWidth: '300px' }}>
                <div style={{
                  fontSize: '1rem',
                  lineHeight: '1.0',
                  margin: '0',
                  color: '#cccccc',
                  fontFamily: 'Telegraf, sans-serif',
                  opacity: '1'
                }}>
                  {service.description.split(', ').map((item, itemIndex) => (
                    <div key={itemIndex} style={{ marginBottom: '0.5rem' }}>
                      {item}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Spacer 75vh */}
      <div style={{ height: '75vh' }} />

      {/* Section 3 - Workflow */}
      <div style={{ height: '200vh', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        {/* Heading - Left aligned */}
        <div style={{
          color: 'white',
          maxWidth: '1900px',
          pointerEvents: 'none',
          userSelect: 'none',
          padding: '0rem 0rem 0rem 14rem',
          borderRadius: '10px',
          alignSelf: 'flex-start',
          paddingLeft: '10vw',
          marginTop: '4rem'
        }}>
          <h1 style={{
            fontSize: '1.5rem',
            fontWeight: '400',
            margin: '0 0 4rem 0',
            color: '#cccccc',
            fontFamily: 'Telegraf, sans-serif'
          }}>
            WORKFLOW
          </h1>
        </div>

        {/* Grid - Centered */}
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gridTemplateRows: '1fr 1fr 1fr 1fr',
            gap: '4rem 8rem',
            maxWidth: '1900px',
            minHeight: '200vh',
            margin: '0 auto',
            alignItems: 'center',
            justifyItems: 'center',
            position: 'relative',
            width: '100%',
          }}
        >
          {/* Step 1 - Top Left */}
          <div
            style={{
              gridColumn: 1,
              gridRow: 1,
              padding: '2.5rem 2rem',
              maxWidth: '500px',
              width: '100%',
            }}
          >
            <h2 style={{
              fontSize: '2rem',
              fontWeight: '600',
              color: '#ffffff',
              margin: '0 0 1rem 0',
              fontFamily: 'Telegraf, sans-serif'
            }}>
              KICKOFF
            </h2>
            <p style={{
              fontSize: '1rem',
              color: '#cccccc',
              margin: 0,
              fontFamily: 'Telegraf, sans-serif',
              lineHeight: 1.5
            }}>
              Wir nehmen uns Zeit, deine Marke wirklich zu verstehen – von deiner Geschichte über deine Werte bis hin zu den Zielen, die du erreichen möchtest. Nur so können wir eine Lösung schaffen, die wirklich zu dir passt.
            </p>
          </div>

          {/* Step 2 - Row 2, Column 2 */}
          <div
            style={{
              gridColumn: 2,
              gridRow: 2,
              borderRadius: '16px',
              padding: '2.5rem 2rem',
              maxWidth: '500px',
              width: '100%',
            }}
          >
            <h2 style={{
              fontSize: '2rem',
              fontWeight: '600',
              color: '#ffffff',
              margin: '0 0 1rem 0',
              fontFamily: 'Telegraf, sans-serif'
            }}>
              ANALYSE
            </h2>
            <p style={{
              fontSize: '1rem',
              color: '#cccccc',
              margin: 0,
              fontFamily: 'Telegraf, sans-serif',
              lineHeight: 1.5
            }}>
              Gemeinsam analysieren wir deine Zielgruppe, den Markt und deine Wettbewerber. Darauf aufbauend entwickeln wir eine klare, individuelle Strategie, die den Rahmen für das gesamte Projekt bildet und sicherstellt, dass wir genau die richtigen Akzente setzen.
            </p>
          </div>

          {/* Step 3 - Row 3, Column 1 */}
          <div
            style={{
              gridColumn: 1,
              gridRow: 3,
              borderRadius: '16px',
              padding: '2.5rem 2rem',
              maxWidth: '500px',
              width: '100%',
            }}
          >
            <h2 style={{
              fontSize: '2rem',
              fontWeight: '600',
              color: '#ffffff',
              margin: '0 0 1rem 0',
              fontFamily: 'Telegraf, sans-serif'
            }}>
              DESIGN & ENTWICKLUNG
            </h2>
            <p style={{
              fontSize: '1rem',
              color: '#cccccc',
              margin: 0,
              fontFamily: 'Telegraf, sans-serif',
              lineHeight: 1.5
            }}>
              Wir verwandeln die Strategie in ein modernes, intuitives Design, das deine Nutzer:innen begeistert und deine Marke stark macht. Dabei achten wir nicht nur auf Optik, sondern auch auf einfache Bedienbarkeit und technische Qualität. So entsteht ein digitales Erlebnis, das sowohl schön als auch funktional ist.
            </p>
          </div>

          {/* Step 4 - Row 4, Column 2 */}
          <div
            style={{
              gridColumn: 2,
              gridRow: 4,
              borderRadius: '16px',
              padding: '2.5rem 2rem',
              maxWidth: '500px',
              width: '100%',
            }}
          >
            <h2 style={{
              fontSize: '2rem',
              fontWeight: '600',
              color: '#fff',
              margin: '0 0 1rem 0',
              fontFamily: 'Telegraf, sans-serif'
            }}>
              LAUNCH & SUPPORT
            </h2>
            <p style={{
              fontSize: '1rem',
              color: '#cccccc',
              margin: 0,
              fontFamily: 'Telegraf, sans-serif',
              lineHeight: 1.5
            }}>
              Wenn alles fertig ist, bekommst du ein rundum fertiges Produkt, das wir gemeinsam prüfen und optimieren. Auch nach dem Launch sind wir an deiner Seite – mit persönlichem Support, regelmäßigen Updates und Anpassungen, damit deine Website immer up to date und leistungsfähig bleibt.
            </p>
          </div>
        </div>
      </div>

      {/* Spacer 75vh */}
      <div style={{ height: '75vh' }} />

      {/* Section 4 - Team Heading Container */}
      <div style={{ height: '100vh', display: 'flex', alignItems: 'flex-end', paddingLeft: '10vw', paddingBottom: '0vh' }}>
        <div style={{
          color: 'white',
          maxWidth: '800px',
          pointerEvents: 'none',
          userSelect: 'none',
          padding: '3rem',
          borderRadius: '10px'
        }}>
          <h1 style={{
            fontSize: '1.5rem',
            fontWeight: '400',
            margin: '0 0 3rem 0',
            color: '#cccccc',
            fontFamily: 'Telegraf, sans-serif'
          }}>
            TEAM
          </h1>
        </div>
      </div>

      {/* Section 4b - Team Members Container */}
      <div style={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div style={{
          color: 'white',
          maxWidth: '1200px',
          pointerEvents: 'none',
          userSelect: 'none',
          padding: '3rem',
          borderRadius: '10px',
          display: 'flex',
          gap: '20rem',
          justifyContent: 'center',
          alignItems: 'center',
          width: '100%'
        }}>
          {/* Philipp's Info - Left side */}
          <div style={{
            textAlign: 'center',
            minWidth: '300px'
          }}>
            <h3 style={{
              fontSize: '1.75rem',
              fontWeight: '500',
              margin: '0 -7rem 1rem 0',
              color: '#ffffff',
              fontFamily: 'Telegraf, sans-serif'
            }}>
              PHILIPP FUCHS
            </h3>
            <p style={{
              fontSize: '1rem',
              lineHeight: '1.6',
              margin: '0 -7rem 0rem 0',
              color: '#cccccc',
              fontFamily: 'Telegraf, sans-serif'
            }}>
              CEO & Founder
            </p>
          </div>

          {/* Maxi's Info - Right side */}
          <div style={{
            textAlign: 'center',
            minWidth: '300px'
          }}>
            <h3 style={{
              fontSize: '2rem',
              fontWeight: '500',
              margin: '0 0 1rem 0',
              color: '#ffffff',
              fontFamily: 'Telegraf, sans-serif'
            }}>
              MAXIMILIAN DIRNBERGER
            </h3>
            <p style={{
              fontSize: '1rem',
              lineHeight: '1.6',
              margin: '0',
              color: '#cccccc',
              fontFamily: 'Telegraf, sans-serif'
            }}>
              Head of Sales
            </p>
          </div>
        </div>
      </div>

      {/* Spacer 50vh */}
      <div style={{ height: '50vh' }} />

      {/* Section 5 - Final CTA */}
      <div style={{ height: '100vh', display: 'flex', alignItems: 'center', paddingLeft: '10vw' }}>
        <div style={{
          color: 'white',
          maxWidth: '1400px',
          pointerEvents: 'none',
          userSelect: 'none',
          padding: '2rem',
          borderRadius: '10px'
        }}>
          <h1 style={{
            fontSize: '7rem',
            fontWeight: 'bold',
            margin: '0 0 2rem 0',
            color: '#ffffff',
            fontFamily: 'Telegraf, sans-serif'
          }}>
            Let's Work Together
          </h1>
          <h2 style={{
            fontSize: '2.5rem',
            fontWeight: '600',
            margin: '0 0 2rem 0',
            color: '#ffffff',
            fontFamily: 'Telegraf, sans-serif'
          }}>
            Deine Idee. Unsere Mission.
          </h2>
          <p style={{
            fontSize: '1.3rem',
            lineHeight: '1.6',
            margin: '0',
            color: '#ffffff',
            fontFamily: 'Telegraf, sans-serif'
          }}>
            Teile deine Idee mit uns und wir bringen sie gemeinsam in die digitale Realität.
          </p>
        </div>
      </div>

      {/* Extra space to allow full scroll */}
      <div style={{ height: '100vh' }} />
      </div>
    </Html>
  );
};


// Main AboutDesign component
const AboutDesign = ({ onBack, onReady }) => {
  useEffect(() => {
    if (onReady) onReady();
  }, [onReady]);

  // Calculate total scroll distance based on content height
  // Each section + spacers = approximately 10 pages worth of scroll
  const totalPages = 10;

  return (
    <div
      className="about-design-container"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        background: '#000000',
        zIndex: 10,
        margin: 0,
        padding: 0,
        overflow: 'hidden'
      }}
    >
      {/* 3D Background Canvas with ScrollControls */}
      <Canvas
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          zIndex: 1,
          pointerEvents: 'auto' // Enable pointer events for ScrollControls
        }}
        gl={{
          antialias: true,
          alpha: false,
          powerPreference: "high-performance"
        }}
        camera={{
          fov: 42,
          position: [0, 0, 5],
          near: 0.1,
          far: 1000
        }}
      >
        <ScrollControls pages={totalPages} damping={0.1}>
          <ScrollSynchronized3DScene />
          <ScrollSynchronizedContent />
        </ScrollControls>
      </Canvas>

      {/* Back button - outside Canvas for interactivity */}
      <button
        className="back-button"
        onClick={onBack}
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          zIndex: 1001, // Higher than content overlay
          color: 'white',
          padding: '10px 20px',
          borderRadius: '5px',
          cursor: 'pointer',
          backdropFilter: 'blur(10px)',
          pointerEvents: 'auto',
          border: 'none',
          background: 'rgba(255, 255, 255, 0.1)'
        }}
      >
        ← Back
      </button>

      {/* Global CSS for retro cinematic effects */}
      <style jsx="true" global="true">{`
        .content-overlay::-webkit-scrollbar {
          display: none;
        }
        .content-overlay {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        
        /* Retro cinematic overlay effect */
        .about-design-container::before {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background: radial-gradient(ellipse at center, transparent 0%, rgba(0,0,0,0.1) 70%, rgba(0,0,0,0.3) 90%, rgba(0,0,0,0.5) 100%);
          pointer-events: none;
          z-index: 50;
        }
        
        /* Film grain effect */
        .about-design-container::after {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05'/%3E%3C/svg%3E");
          pointer-events: none;
          z-index: 51;
          opacity: 0.1;
        }
        
        /* Subtle color grading */
        .about-design-container {
          filter: contrast(1.1) saturate(1.05);
        }
        

      `}</style>
      
      {/* Back button */}
      <button
        className="back-button"
        onClick={onBack}
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          zIndex: 1001, // Higher than content overlay
          color: 'white',
          padding: '10px 20px',
          borderRadius: '5px',
          cursor: 'pointer',
          backdropFilter: 'blur(10px)',
          pointerEvents: 'auto',
          border: 'none',
          background: 'rgba(255, 255, 255, 0.1)'
        }}
      >
        ← Back
      </button>




    </div>
  );
};

export default AboutDesign;