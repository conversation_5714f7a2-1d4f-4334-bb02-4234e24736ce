import { useEffect, useRef } from 'react';
import { useLenis } from 'lenis/react';

export const useLenisScroll = (onScroll, onScrollStop) => {
  const lenis = useLenis();
  const savedOnScroll = useRef(onScroll);
  const savedOnScrollStop = useRef(onScrollStop);
  const scrollStopTimer = useRef(null);
  const isInitialMount = useRef(true);

  // Reset scroll position on initial mount
  useEffect(() => {
    if (lenis && isInitialMount.current) {
      isInitialMount.current = false;
      // Reset both window and Lenis scroll positions
      window.scrollTo(0, 0);
      lenis.scrollTo(0, { immediate: true });
      
      // Force a reflow to ensure the scroll position is applied
      requestAnimationFrame(() => {
        window.scrollTo(0, 0);
        lenis.scrollTo(0, { immediate: true });
      });
    }
  }, [lenis]);

  useEffect(() => {
    savedOnScroll.current = onScroll;
    savedOnScrollStop.current = onScrollStop;
  }, [onScroll, onScrollStop]);

  useEffect(() => {
    if (!lenis || !onScroll) return;

    const handleScroll = (e) => {
      const { scroll, limit } = e;
      let progress = scroll / limit;

      // Virtual endless loop: normalize progress to stay within 0-1
      if (progress < 0 || progress > 1) {
        progress = progress - Math.floor(progress);
      }

      if (savedOnScroll.current) {
        savedOnScroll.current(progress);
      }

      // Detect scroll stop
      if (savedOnScrollStop.current) {
        clearTimeout(scrollStopTimer.current);
        scrollStopTimer.current = setTimeout(() => {
          savedOnScrollStop.current(progress);
        }, 150); // 150ms delay to detect scroll stop
      }
    };

    lenis.on('scroll', handleScroll);

    return () => {
      lenis.off('scroll', handleScroll);
    };
  }, [lenis, onScroll, onScrollStop]);

  return lenis;
};
