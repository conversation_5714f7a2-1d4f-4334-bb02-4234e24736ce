import React, { useRef, useEffect } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import * as THREE from 'three';
import { BoxGeometry, Color, Mesh, NodeMaterial, PerspectiveCamera, Scene, WebGPURenderer } from 'three/webgpu';
import { Fn, normalLocal, vec4, positionLocal, positionWorld, vec2, vec3, mix, smoothstep, cameraProjectionMatrix, uniform, distance, texture, uv, screenUV, varying, modelViewMatrix, float, cos } from 'three/tsl';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { TrailCanvasClass } from './TrailCanvas';

const TrailBackground = () => {
  const { camera, size, gl, scene } = useThree();
  const trailRef = useRef(null);
  const trailTextureRef = useRef(null);
  const mouseRef = useRef(new THREE.Vector3());
  const mouse2DRef = useRef(new THREE.Vector2());
  const dummyRef = useRef(null);
  const raycasterRef = useRef(null);
  const materialsRef = useRef([]);
  const modelRef = useRef(null);
  
  // Add mouse uniform setup EXACTLY like original Akella
  const uMouse = uniform(mouseRef.current, 'vec3');

  // Add the MISSING helper functions from original Akella
  const palette = Fn(({t}) => {
    const a = vec3(0.5,0.5,0.5);
    const b = vec3(0.5,0.5,0.5);
    const c = vec3(1.0,1.0,1.0);
    const d = vec3(0.00,0.10,0.20);

    return a.add(b.mul(cos(float(6.283185).mul(c.mul(t).add(d)))));
  });

  const sRGBTransferOETF = Fn( ( [ color ] ) => {
    const a = color.pow( 0.41666 ).mul( 1.055 ).sub( 0.055 );
    const b = color.mul( 12.92 );
    const factor = color.lessThanEqual( 0.0031308 );
  
    const rgbResult = mix( a, b, factor );
  
    return rgbResult;
  });

  // Initialize everything exactly as in the original working code
  useEffect(() => {
    if (size.width && size.height && scene) {
      console.log('=== TRAILBACKGROUND INITIALIZATION ===');
      
      // Create raycaster exactly as in original
      raycasterRef.current = new THREE.Raycaster();
      
      // Create trail canvas exactly as in original
      trailRef.current = new TrailCanvasClass(size.width, size.height);  // Use actual screen dimensions like original Akella
      
      // Add canvas to DOM exactly as in original
      const canv = trailRef.current.canvas;
      canv.style.position = 'absolute';
      canv.style.top = '0';
      canv.style.left = '0';
      canv.style.zIndex = '1000';
      canv.style.width = '200px';  // Fixed width like original Akella
      canv.style.height = `${200 * size.height / size.width}px`;  // Proportional height like original Akella
      document.body.appendChild(canv);
      
      // Create trail texture exactly as in original
      const trailCanvas = trailRef.current.getTexture();
      trailTextureRef.current = new THREE.Texture(trailCanvas);
      trailTextureRef.current.flipY = false;
      trailTextureRef.current.needsUpdate = true;
      
      // Create dummy object exactly as in original
      dummyRef.current = new THREE.Mesh(
        new THREE.PlaneGeometry(19, 19),
        new THREE.MeshBasicMaterial({ 
          color: 0x00ff00,
          transparent: true,
          opacity: 0.0  // Make it completely invisible
        })
      );
      dummyRef.current.position.set(0, 0, 0);
      scene.add(dummyRef.current);
      
      // Load GLB model exactly as in original
      const dracoLoader = new DRACOLoader();
      dracoLoader.setDecoderPath('https://www.gstatic.com/draco/versioned/decoders/1.5.6/');
      
      const loader = new GLTFLoader();
      loader.setDRACOLoader(dracoLoader);
      
      loader.load('/models/original.glb', (gltf) => {
        console.log('=== GLB MODEL LOADED ===');
        const model = gltf.scene;
        modelRef.current = model;
        
        // Position model exactly as in original
        model.position.set(0, 1, 0);
        
        // Scale up the model by 100% (double size) to make it more visible
        model.scale.set(1.25, 1.25, 1.25);
        
        scene.add(model);
        
        // Process all materials with COMPLETE Akella shader implementation
        model.traverse((child) => {
          if (child.isMesh && child.material) {
            try {
              // Create NodeMaterial with COMPLETE original implementation
              let material = new NodeMaterial();
              let texture1 = child.material.map;
              let texture2 = child.material.emissiveMap;
              let uvscreen = varying(vec2(0.0, 0.0));
              
              // EXACT positionNode from original Akella
              material.positionNode = Fn(() => {
                const pos = positionLocal;
                const ndc = cameraProjectionMatrix.mul(modelViewMatrix).mul(vec4(pos, 1.0));
                uvscreen.assign(ndc.xy.div(ndc.w).add(1.0).div(2.0));
                uvscreen.y = uvscreen.y.oneMinus();
                
                const extrude = texture(trailTextureRef.current, uvscreen).r;
                pos.z.mulAssign(mix(0.03, 1.0, extrude));
                
                return pos;
              })();
              
              // COMPLETE colorNode from original Akella - THIS WAS MISSING!
              material.colorNode = Fn(() => {
                const dist = distance(positionWorld, uMouse);
                
                // Handle cases where textures might be null
                const tt1 = texture1 ? sRGBTransferOETF(texture(texture1, uv())) : vec4(1.0, 1.0, 1.0, 1.0);
                const tt2 = texture2 ? sRGBTransferOETF(texture(texture2, uv())) : vec4(0.0, 0.0, 0.0, 1.0);
                
                const extrude = texture(trailTextureRef.current, screenUV);
                
                let level0 = tt2.b;
                let level1 = tt2.g;
                let level2 = tt2.r;
                let level3 = tt1.b;
                let level4 = tt1.g;
                let level5 = tt1.r;
                
                let final = level0;
                final = mix(final, level1, smoothstep(0.0, 0.2, extrude));
                final = mix(final, level2, smoothstep(0.2, 0.4, extrude));
                final = mix(final, level3, smoothstep(0.4, 0.6, extrude));
                final = mix(final, level4, smoothstep(0.6, 0.8, extrude));
                final = mix(final, level5, smoothstep(0.8, 1.0, extrude));

                let finalCool = palette({t: final.mul(1.0).add(0.0)});

                return vec4(vec3(final), 1.0);
              })();
              
              child.material = material;
              materialsRef.current.push(material);
              
              console.log('Applied COMPLETE NodeMaterial from original Akella to:', child.name);
            } catch (error) {
              console.error('NodeMaterial failed:', error);
              
              // Fallback to basic material
              const fallbackMaterial = new THREE.MeshBasicMaterial({ 
                color: 0xff0000,
                transparent: true,
                opacity: 0.8
              });
              
              child.material = fallbackMaterial;
              materialsRef.current.push(fallbackMaterial);
            }
          }
        });
      }, (progress) => {
        console.log('Loading progress:', progress);
      }, (error) => {
        console.error('Error loading GLB model:', error);
      });

      return () => {
        // Cleanup
        if (canv && document.body.contains(canv)) {
          document.body.removeChild(canv);
        }
      };
    }
  }, [size.width, size.height, scene, uMouse, palette, sRGBTransferOETF]);

  // Mouse tracking exactly as in original
  useEffect(() => {
    const handleMouseMove = (event) => {
      if (trailRef.current && raycasterRef.current && dummyRef.current) {
        // Convert mouse position exactly as in original Akella
        let mouseX = (event.clientX / size.width) * 2 - 1;
        let mouseY = -(event.clientY / size.height) * 2 + 1;
        
        // Raycast exactly as in original Akella
        raycasterRef.current.setFromCamera(new THREE.Vector2(mouseX, mouseY), camera);
        const intersects = raycasterRef.current.intersectObjects([dummyRef.current]);
        
        if (intersects.length > 0) {
          // Update 3D mouse position for shader - THIS WAS MISSING!
          mouseRef.current.copy(intersects[0].point);
          
          // Update trail canvas with 2D coordinates
          mouse2DRef.current.set(event.clientX, event.clientY);
          trailRef.current.update({ x: event.clientX, y: event.clientY });
          
          // Update trail texture
          if (trailTextureRef.current) {
            trailTextureRef.current.needsUpdate = true;
          }
        }
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, [camera, size.width, size.height]);

  // Animation loop exactly as in original - WITH MISSING MOUSE UPDATE
  useFrame((state) => {
    if (trailRef.current) {
      // Update trail canvas
      trailRef.current.update(mouse2DRef.current);
      
      if (trailTextureRef.current) {
        trailTextureRef.current.needsUpdate = true;
      }
      
      // Fallback circular animation - EXACTLY like original Akella
      const playhead = state.clock.getElapsedTime() / 60.0; // Use same timing as original
      
      let x = Math.sin(playhead * 2.0 * Math.PI) * 0.5;
      let y = Math.cos(playhead * 2.0 * Math.PI) * 0.5;
      
      mouse2DRef.current.set(
        (x + 1) * 0.5 * size.width,
        (y + 1) * 0.5 * size.height
      );
      
      // THIS WAS MISSING - Update 3D mouse position for shader!
      if (raycasterRef.current && dummyRef.current) {
        raycasterRef.current.setFromCamera(new THREE.Vector2(x, y), camera);
        const intersects = raycasterRef.current.intersectObjects([dummyRef.current]);
        if (intersects.length > 0) {
          mouseRef.current.copy(intersects[0].point);
        }
      }
    }
  });

  return (
    <>
      {/* Add lights for better visibility */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[5, 5, 5]} intensity={0.8} />
      <pointLight position={[0, 5, 0]} intensity={0.5} />
    </>
  );
};

export default TrailBackground;
