import React from 'react';
import './AppAIPlaceholder.css';
import WaterMatcapBackground from './components/WaterMatcap/WaterMatcapBackgroundPlaceholder';

const AppAIPlaceholder = ({ onBack }) => {
  const handleBackClick = () => {
    if (onBack) {
      onBack();
    }
  };

  return (
    <div className="app-ai-container">
      {/* Background */}
      <div className="background-container">
        <WaterMatcapBackground />
      </div>
      
      {/* Back button in top left corner */}
      <button className="back-button" onClick={handleBackClick}>
        ← BACK
      </button>
      
      {/* Centered content */}
      <div className="center-content">
        <h1 className="main-title">BLCKS.AI</h1>
        <p className="subtitle">KI-Automatisierungen & digitale Lösungen</p>
        <p className="description">
            COMING SOON - WIR ERNEUERN BLCKS.AI
        </p>
        <a href="mailto:<EMAIL>" className="email-button">
          [ <EMAIL> ]
        </a>
      </div>
    </div>
  );
};

export default AppAIPlaceholder;
