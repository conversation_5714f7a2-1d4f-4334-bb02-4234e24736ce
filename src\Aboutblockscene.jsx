/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.5.3 aboutblockscene.glb 
*/

import React, { useRef, useEffect } from 'react'
import { useGLTF, PerspectiveCamera, useAnimations, useTexture } from '@react-three/drei'
import { useFrame } from '@react-three/fiber'
import * as THREE from 'three'

export default function Model(props) {
  const group = React.useRef()
  const cameraRef = useRef()
  const { nodes, materials, animations } = useGLTF('/aboutblockscene.glb')
  const { actions } = useAnimations(animations, group)
  
  // Load the tiledcubes texture
  const tiledcubesTexture = useTexture('/textures/tiledcubes.webp', (texture) => {
    console.log('Texture loaded successfully:', texture)
    // Enable texture repeat if needed
    texture.wrapS = THREE.RepeatWrapping
    texture.wrapT = THREE.RepeatWrapping
    texture.repeat.set(1, 1) // Adjust these values to control tiling
  }, (error) => {
    console.error('Error loading texture:', error)
  })
  
  // Create a material with the tiledcubes texture
  const texturedMaterial = new THREE.MeshStandardMaterial({
    map: tiledcubesTexture,
    roughness: 0.8,
    metalness: 0.2,
    color: 0xffffff, // Keep some white color as fallback
  })
  
  // Debug logging
  console.log('Texture object:', tiledcubesTexture)
  console.log('Material created:', texturedMaterial)
  
  // Apply camera animation based on scroll progress from parent
  useFrame(() => {
    if (cameraRef.current && props.scrollProgress !== undefined) {
      // Debug: log scroll progress
      console.log('Scroll progress received:', props.scrollProgress)
      
      if (animations && animations.length > 0) {
        try {
          // Get the first animation (assuming it's the camera animation)
          const animation = animations[0]
          console.log('Animation duration:', animation.duration)
          
          if (animation && animation.tracks && Array.isArray(animation.tracks)) {
            // Find camera position and rotation tracks
            const positionTrack = animation.tracks.find(track => 
              track && track.name && (
                track.name === 'Camera.position' || 
                track.name.includes('position') ||
                track.name.includes('Camera')
              )
            )
            const rotationTrack = animation.tracks.find(track => 
              track && track.name && (
                track.name === 'Camera.quaternion' || 
                track.name === 'Camera.rotation' || 
                track.name.includes('rotation') ||
                track.name.includes('quaternion')
              )
            )
            
            console.log('Available tracks:', animation.tracks.map(t => t.name))
            console.log('Position track found:', positionTrack ? positionTrack.name : 'none')
            console.log('Rotation track found:', rotationTrack ? rotationTrack.name : 'none')
            
            // Calculate animation time based on scroll progress
            const animationTime = props.scrollProgress * animation.duration
            console.log('Animation time:', animationTime)
            
            // Apply position animation
            if (positionTrack && positionTrack.times && positionTrack.values) {
              const position = cameraRef.current.position
              
              // Find the keyframe indices for interpolation
              let keyframeIndex = 0
              for (let i = 0; i < positionTrack.times.length - 1; i++) {
                if (animationTime >= positionTrack.times[i] && animationTime <= positionTrack.times[i + 1]) {
                  keyframeIndex = i
                  break
                }
                if (animationTime > positionTrack.times[i]) {
                  keyframeIndex = i
                }
              }
              
              const nextIndex = Math.min(keyframeIndex + 1, positionTrack.times.length - 1)
              
              // Calculate interpolation factor
              let t = 0
              if (keyframeIndex !== nextIndex) {
                const timeDiff = positionTrack.times[nextIndex] - positionTrack.times[keyframeIndex]
                if (timeDiff > 0) {
                  t = (animationTime - positionTrack.times[keyframeIndex]) / timeDiff
                }
              }
              
              // Get current and next positions
              const currentPos = [
                positionTrack.values[keyframeIndex * 3] || 0,
                positionTrack.values[keyframeIndex * 3 + 1] || 0,
                positionTrack.values[keyframeIndex * 3 + 2] || 0
              ]
              const nextPos = [
                positionTrack.values[nextIndex * 3] || 0,
                positionTrack.values[nextIndex * 3 + 1] || 0,
                positionTrack.values[nextIndex * 3 + 2] || 0
              ]
              
              // Interpolate position
              position.set(
                currentPos[0] + (nextPos[0] - currentPos[0]) * t,
                currentPos[1] + (nextPos[1] - currentPos[1]) * t,
                currentPos[2] + (nextPos[2] - currentPos[2]) * t
              )
              
              console.log('Camera position updated to:', position.toArray())
            }
            
            // Apply rotation animation
            if (rotationTrack && rotationTrack.times && rotationTrack.values) {
              const rotation = cameraRef.current.rotation
              
              // Find the keyframe indices for interpolation
              let keyframeIndex = 0
              for (let i = 0; i < rotationTrack.times.length - 1; i++) {
                if (animationTime >= rotationTrack.times[i] && animationTime <= rotationTrack.times[i + 1]) {
                  keyframeIndex = i
                  break
                }
                if (animationTime > rotationTrack.times[i]) {
                  keyframeIndex = i
                }
              }
              
              const nextIndex = Math.min(keyframeIndex + 1, rotationTrack.times.length - 1)
              
              // Calculate interpolation factor
              let t = 0
              if (keyframeIndex !== nextIndex) {
                const timeDiff = rotationTrack.times[nextIndex] - rotationTrack.times[keyframeIndex]
                if (timeDiff > 0) {
                  t = (animationTime - rotationTrack.times[keyframeIndex]) / timeDiff
                }
              }
              
              // Handle quaternion (4 values) vs euler (3 values)
              const isQuaternion = rotationTrack.values.length / rotationTrack.times.length === 4
              
              if (isQuaternion) {
                // Quaternion interpolation
                const currentRot = new THREE.Quaternion(
                  rotationTrack.values[keyframeIndex * 4] || 0,
                  rotationTrack.values[keyframeIndex * 4 + 1] || 0,
                  rotationTrack.values[keyframeIndex * 4 + 2] || 0,
                  rotationTrack.values[keyframeIndex * 4 + 3] || 1
                )
                const nextRot = new THREE.Quaternion(
                  rotationTrack.values[nextIndex * 4] || 0,
                  rotationTrack.values[nextIndex * 4 + 1] || 0,
                  rotationTrack.values[nextIndex * 4 + 2] || 0,
                  rotationTrack.values[nextIndex * 4 + 3] || 1
                )
                
                // Spherical linear interpolation
                const interpolatedQuat = new THREE.Quaternion().slerpQuaternions(currentRot, nextRot, t)
                rotation.setFromQuaternion(interpolatedQuat)
              } else {
                // Euler angle interpolation
                const currentRot = [
                  rotationTrack.values[keyframeIndex * 3] || 0,
                  rotationTrack.values[keyframeIndex * 3 + 1] || 0,
                  rotationTrack.values[keyframeIndex * 3 + 2] || 0
                ]
                const nextRot = [
                  rotationTrack.values[nextIndex * 3] || 0,
                  rotationTrack.values[nextIndex * 3 + 1] || 0,
                  rotationTrack.values[nextIndex * 3 + 2] || 0
                ]
                
                rotation.set(
                  currentRot[0] + (nextRot[0] - currentRot[0]) * t,
                  currentRot[1] + (nextRot[1] - currentRot[1]) * t,
                  currentRot[2] + (nextRot[2] - currentRot[2]) * t
                )
              }
              
              console.log('Camera rotation updated')
            }
          }
        } catch (error) {
          console.error('Error in camera animation:', error)
        }
      } else {
        console.log('No animations found')
      }
    }
  })

  // Alternative: Use the built-in animation mixer if the above doesn't work
  useEffect(() => {
    if (actions && Object.keys(actions).length > 0) {
      // Get the first action
      const actionName = Object.keys(actions)[0]
      const action = actions[actionName]
      
      if (action) {
        action.play()
        action.paused = true // We'll control it manually
        console.log('Animation action found:', actionName, 'Duration:', action.getClip().duration)
      }
    }
  }, [actions])

  // Update animation mixer time based on scroll
  useFrame(() => {
    if (actions && Object.keys(actions).length > 0 && props.scrollProgress !== undefined) {
      const actionName = Object.keys(actions)[0]
      const action = actions[actionName]
      
      if (action) {
        const duration = action.getClip().duration
        action.time = props.scrollProgress * duration
        console.log('Mixer time set to:', action.time, 'out of', duration)
      }
    }
  })

  return (
    <group ref={group} {...props} dispose={null}>
      {/* Add lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 5]} intensity={1} castShadow />
      
      <group name="Scene">
        <group name="world" position={[1, 0, 0.1]}>
          <mesh name="tile2" geometry={nodes.tile2.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world001" position={[3, 0, 0]}>
          <mesh name="tile2001" geometry={nodes.tile2001.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world002" position={[-1, 0, 0]}>
          <mesh name="tile2002" geometry={nodes.tile2002.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world003" position={[-3, 0, 0]}>
          <mesh name="tile2003" geometry={nodes.tile2003.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world004" position={[5, 0, 0]}>
          <mesh name="tile2004" geometry={nodes.tile2004.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world005" position={[-5, 0, 0]}>
          <mesh name="tile2005" geometry={nodes.tile2005.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world006" position={[1, -2, 0]}>
          <mesh name="tile2006" geometry={nodes.tile2006.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world007" position={[-1, -2, 0]}>
          <mesh name="tile2007" geometry={nodes.tile2007.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world008" position={[-3, -2, 0]}>
          <mesh name="tile2008" geometry={nodes.tile2008.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world009" position={[3, -2, 0]}>
          <mesh name="tile2009" geometry={nodes.tile2009.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world010" position={[-5, -2, 0]}>
          <mesh name="tile2010" geometry={nodes.tile2010.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world011" position={[5, -2, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2011" geometry={nodes.tile2011.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world012" position={[1, -4, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2012" geometry={nodes.tile2012.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world013" position={[-1, -4, 0]}>
          <mesh name="tile2013" geometry={nodes.tile2013.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world014" position={[3, -4, 0]}>
          <mesh name="tile2014" geometry={nodes.tile2014.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world015" position={[-3, -4, 0]}>
          <mesh name="tile2015" geometry={nodes.tile2015.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world016" position={[-5, -4, 0]}>
          <mesh name="tile2016" geometry={nodes.tile2016.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world017" position={[5, -4, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2017" geometry={nodes.tile2017.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world018" position={[-1, -6, 0]}>
          <mesh name="tile2018" geometry={nodes.tile2018.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world019" position={[1, -6, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2019" geometry={nodes.tile2019.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world020" position={[3, -6, 0]}>
          <mesh name="tile2020" geometry={nodes.tile2020.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world021" position={[-3, -6, 0]}>
          <mesh name="tile2021" geometry={nodes.tile2021.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world022" position={[-5, -6, 0]}>
          <mesh name="tile2022" geometry={nodes.tile2022.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world023" position={[5, -6, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2023" geometry={nodes.tile2023.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world024" position={[1, -8, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2024" geometry={nodes.tile2024.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world025" position={[-1, -8, 0]}>
          <mesh name="tile2025" geometry={nodes.tile2025.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world026" position={[3, -8, 0]}>
          <mesh name="tile2026" geometry={nodes.tile2026.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world027" position={[-5, -8, 0]}>
          <mesh name="tile2027" geometry={nodes.tile2027.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world028" position={[-3, -8, 0]}>
          <mesh name="tile2028" geometry={nodes.tile2028.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world029" position={[5, -8, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2029" geometry={nodes.tile2029.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world030" position={[1, -10, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2030" geometry={nodes.tile2030.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world031" position={[-1, -10, 0]}>
          <mesh name="tile2031" geometry={nodes.tile2031.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world032" position={[5, -10, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2032" geometry={nodes.tile2032.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world033" position={[-3, -10, 0]}>
          <mesh name="tile2033" geometry={nodes.tile2033.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world034" position={[-5, -10, 0]}>
          <mesh name="tile2034" geometry={nodes.tile2034.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world035" position={[3, -10, 0]}>
          <mesh name="tile2035" geometry={nodes.tile2035.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world036" position={[3, -12, 0]}>
          <mesh name="tile2036" geometry={nodes.tile2036.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world037" position={[-5, -12, 0]}>
          <mesh name="tile2037" geometry={nodes.tile2037.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world038" position={[-3, -12, 0]}>
          <mesh name="tile2038" geometry={nodes.tile2038.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world039" position={[5, -12, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2039" geometry={nodes.tile2039.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world040" position={[-1, -12, 0]}>
          <mesh name="tile2040" geometry={nodes.tile2040.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world041" position={[1, -12, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2041" geometry={nodes.tile2041.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world042" position={[1, -14, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2042" geometry={nodes.tile2042.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world043" position={[-1, -14, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2043" geometry={nodes.tile2043.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world044" position={[-3, -14, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2044" geometry={nodes.tile2044.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world045" position={[-5, -14, 0]}>
          <mesh name="tile2045" geometry={nodes.tile2045.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world046" position={[5, -14, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2046" geometry={nodes.tile2046.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world047" position={[3, -14, 0]}>
          <mesh name="tile2047" geometry={nodes.tile2047.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world048" position={[-1, -16, 0]}>
          <mesh name="tile2048" geometry={nodes.tile2048.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world049" position={[1, -16, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2049" geometry={nodes.tile2049.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world050" position={[3, -16, 0]}>
          <mesh name="tile2050" geometry={nodes.tile2050.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world051" position={[5, -16, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2051" geometry={nodes.tile2051.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world052" position={[-5, -16, 0]}>
          <mesh name="tile2052" geometry={nodes.tile2052.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world053" position={[-3, -16, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2053" geometry={nodes.tile2053.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world054" position={[-3, -18, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2054" geometry={nodes.tile2054.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world055" position={[-5, -18, 0]}>
          <mesh name="tile2055" geometry={nodes.tile2055.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world056" position={[5, -18, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2056" geometry={nodes.tile2056.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world057" position={[3, -18, 0]}>
          <mesh name="tile2057" geometry={nodes.tile2057.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world058" position={[1, -18, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2058" geometry={nodes.tile2058.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world059" position={[-1, -18, 0]}>
          <mesh name="tile2059" geometry={nodes.tile2059.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world060" position={[-1, -20, 0]}>
          <mesh name="tile2060" geometry={nodes.tile2060.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world061" position={[1, -20, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2061" geometry={nodes.tile2061.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world062" position={[3, -20, 0]}>
          <mesh name="tile2062" geometry={nodes.tile2062.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world063" position={[5, -20, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2063" geometry={nodes.tile2063.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world064" position={[-5, -20, 0]}>
          <mesh name="tile2064" geometry={nodes.tile2064.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world065" position={[-3, -20, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2065" geometry={nodes.tile2065.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world066" position={[-3, -26, -3]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2066" geometry={nodes.tile2066.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world067" position={[-5, -26, -3]}>
          <mesh name="tile2067" geometry={nodes.tile2067.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world068" position={[5, -26, -3]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2068" geometry={nodes.tile2068.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world069" position={[3, -26, -3]}>
          <mesh name="tile2069" geometry={nodes.tile2069.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world070" position={[1, -26, -3]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2070" geometry={nodes.tile2070.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world071" position={[-1, -26, -3]}>
          <mesh name="tile2071" geometry={nodes.tile2071.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world072" position={[-1, -24, -3]}>
          <mesh name="tile2072" geometry={nodes.tile2072.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world073" position={[1, -24, -3]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2073" geometry={nodes.tile2073.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world074" position={[3, -24, -3]}>
          <mesh name="tile2074" geometry={nodes.tile2074.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world075" position={[5, -24, -3]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2075" geometry={nodes.tile2075.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world076" position={[-5, -24, -3]}>
          <mesh name="tile2076" geometry={nodes.tile2076.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world077" position={[-3, -24, -3]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2077" geometry={nodes.tile2077.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world078" position={[-3, -22, -3]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2078" geometry={nodes.tile2078.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world079" position={[-5, -22, -3]}>
          <mesh name="tile2079" geometry={nodes.tile2079.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world080" position={[5, -22, -2]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2080" geometry={nodes.tile2080.geometry} material={texturedMaterial} position={[0, 0, -1]} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world081" position={[3, -22, -2]}>
          <mesh name="tile2081" geometry={nodes.tile2081.geometry} material={texturedMaterial} position={[0, 0, -1]} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world082" position={[1, -22, -3]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2082" geometry={nodes.tile2082.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world083" position={[-1, -22, -3]}>
          <mesh name="tile2083" geometry={nodes.tile2083.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world084" position={[-3, -28, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2084" geometry={nodes.tile2084.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world085" position={[-5, -28, 0]}>
          <mesh name="tile2085" geometry={nodes.tile2085.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world086" position={[5, -28, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2086" geometry={nodes.tile2086.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world087" position={[3, -28, 0]}>
          <mesh name="tile2087" geometry={nodes.tile2087.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world088" position={[1, -28, 0]} rotation={[0, 0, Math.PI]}>
          <mesh name="tile2088" geometry={nodes.tile2088.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world089" position={[-1, -28, 0]}>
          <mesh name="tile2089" geometry={nodes.tile2089.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world090" position={[-5, 2, 0]}>
          <mesh name="tile2090" geometry={nodes.tile2090.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world091" position={[5, 2, 0]}>
          <mesh name="tile2091" geometry={nodes.tile2091.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world092" position={[-3, 2, 0]}>
          <mesh name="tile2092" geometry={nodes.tile2092.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world093" position={[-1, 2, 0]}>
          <mesh name="tile2093" geometry={nodes.tile2093.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        <group name="world094" position={[3, 2, 0]}>
          <mesh name="tile2094" geometry={nodes.tile2094.geometry} material={texturedMaterial} rotation={[Math.PI / 2, 0, 0]} />
        </group>
        <group name="world095" position={[1, 2, 0]}>
          <mesh name="tile2095" geometry={nodes.tile2095.geometry} material={texturedMaterial} rotation={[-Math.PI / 2, 0, Math.PI]} />
        </group>
        
        {/* Team Phil Draco Model */}
        <TeamPhilModel position={[-1.25, -24, 0]} />
        
        {/* Team Maxi Test Draco Model */}
        <TeamMaxiModel position={[1.25, -24, 0]} />
        
        {/* Animated camera that follows the animation path based on scroll */}
        <PerspectiveCamera 
          ref={cameraRef}
          name="Camera" 
          makeDefault={true} 
          far={1000} 
          near={0.1} 
          fov={22.895} 
          position={[0, -0.5, 10]} 
        />
      </group>
    </group>
  )
}

// Team Phil Draco Model Component
function TeamPhilModel({ position }) {
  const { nodes, materials, animations } = useGLTF('/models/team_phil_draco.glb')
  const { actions } = useAnimations(animations)
  
  useEffect(() => {
    // Play animations if they exist
    if (actions && Object.keys(actions).length > 0) {
      Object.values(actions).forEach(action => {
        if (action) {
          action.play()
        }
      })
    }
  }, [actions])
  
  return (
    <group position={position} scale={[1, 1, 1]}>
      <primitive object={nodes.Scene || nodes.root || nodes.default} />
    </group>
  )
}

// Team Maxi Test Draco Model Component
function TeamMaxiModel({ position }) {
  const { nodes, materials, animations } = useGLTF('/models/team_maxitest_draco.glb')
  const { actions } = useAnimations(animations)
  
  useEffect(() => {
    // Play animations if they exist
    if (actions && Object.keys(actions).length > 0) {
      Object.values(actions).forEach(action => {
        if (action) {
          action.play()
        }
      })
    }
  }, [actions])
  
  return (
    <group position={position} scale={[1, 1, 1]}>
      <primitive object={nodes.Scene || nodes.root || nodes.default} />
    </group>
  )
}

useGLTF.preload('/aboutblockscene.glb')
useGLTF.preload('/models/team_phil_draco.glb')
useGLTF.preload('/models/team_maxitest_draco.glb')
