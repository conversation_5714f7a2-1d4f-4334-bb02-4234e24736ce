// AboutDesign.js - SIMPLE FIX
import React, { useRef, useState, useEffect } from 'react';
import { Canvas, extend, useThree } from '@react-three/fiber';
import { OrbitControls, useGLTF, Text, Html } from '@react-three/drei';
import { <PERSON><PERSON><PERSON>pose<PERSON>, <PERSON>, DepthOfField } from '@react-three/postprocessing';
import * as THREE from 'three';
import './AboutDesign.module.css';

// ----------- TUNABLE CONSTANTS -----------
const CAMERA_Z = 6;              // distance from the wall
const SCROLL_TO_WORLD = 0.01;    // world units per px of scroll (increased for better movement)
// ----------------------------------------

// Light component
const SceneLights = () => {
  return (
    <>
      <color attach="background" args={['#000000']} />
      <directionalLight
        position={[0, 5, 5]}
        intensity={0.03}
        castShadow={false}
        color="#ffffff"
      />
    </>
  );
};

// Extend Three.js helpers
extend({ AxesHelper: THREE.AxesHelper });

// Smooth animation utility
const smoothMove = (object, targetZ, duration = 0.3) => {
  const startZ = object.position.z;
  const startTime = Date.now();
  const animate = () => {
    const elapsed = (Date.now() - startTime) / 1000;
    const progress = Math.min(elapsed / duration, 1);
    const easeOutCubic = 1 - Math.pow(1 - progress, 3);
    object.position.z = startZ + (targetZ - startZ) * easeOutCubic;
    if (progress < 1) requestAnimationFrame(animate);
  };
  requestAnimationFrame(animate);
};

// Single model instance component - COMPLETELY STATIC
const ModelInstance = React.memo(({ position, rotation }) => {
  const groupRef = useRef();
  const { scene } = useGLTF('/models/aboutblocks1.glb');
  const [isHovered, setIsHovered] = useState(false);
  const materialsApplied = useRef(false);

  // Fixed positions - never change
  const fixedPosition = React.useMemo(() => position, []);
  const fixedRotation = React.useMemo(() => rotation, []);

  const handlePointerEnter = () => {
    if (groupRef.current && !isHovered) {
      setIsHovered(true);
      smoothMove(groupRef.current, fixedPosition[2] - 0.6, 0.4); // Move back 0.6 units on hover
    }
  };

  const handlePointerLeave = () => {
    if (groupRef.current && isHovered) {
      setIsHovered(false);
      smoothMove(groupRef.current, fixedPosition[2], 0.8); // Move back to original position
    }
  };

  // Apply custom material ONCE and never change
  useEffect(() => {
    if (groupRef.current && !materialsApplied.current) {
      materialsApplied.current = true;
      groupRef.current.traverse((child) => {
        if (child.isMesh) {
          const customMaterial = new THREE.MeshStandardMaterial({
            color: '#ffffff',
            metalness: 0,
            roughness: 1,
            emissive: '#1a1a1a',
            emissiveIntensity: 0.1,
            transparent: false,
            opacity: 1,
          });
          child.material = customMaterial;
          child.castShadow = false;
          child.receiveShadow = false;
        }
      });
    }
  }, []); // Empty dependency array - run only once

  return (
    <group
      ref={groupRef}
      position={fixedPosition}
      rotation={fixedRotation}
      scale={[1, 1, 1]}
      onPointerEnter={handlePointerEnter}
      onPointerLeave={handlePointerLeave}
    >
      <primitive object={scene.clone()} />
    </group>
  );
});

// STATIC Grid of models (vertical wall) - NEVER MOVES
const AboutBlocksModelGrid = React.memo(() => {
  const { scene } = useGLTF('/models/aboutblocks1.glb');
  
  // Create models ONCE using useMemo to prevent recreation
  const models = React.useMemo(() => {
    const modelArray = [];
    const gridWidth = 6;
    const gridHeight = 50;
    const spacing = 2.01;

    for (let x = 0; x < gridWidth; x++) {
      for (let y = 0; y < gridHeight; y++) {
        const positionX = (x - (gridWidth - 1) / 2) * spacing;
        const positionY = (gridHeight - 1) / 2 - y * spacing;

        modelArray.push(
          <ModelInstance
            key={`model-${x}-${y}`}
            position={[positionX, positionY, 0]}
            rotation={[Math.PI / 2, 0, 0]} // All models have same rotation - no variation
          />
        );
      }
    }
    return modelArray;
  }, [scene]);

  // Return static group that NEVER moves
  return <group position={[0, 0, 0]}>{models}</group>;
});

// Camera controller - ONLY moves camera, NEVER touches the grid
const CameraController = ({ scrollY }) => {
  const { camera } = useThree();

  useEffect(() => {
    // MUCH SLOWER camera movement - only move a small amount relative to scroll
    // This creates the effect of the wall standing still while camera slowly descends
    const targetY = scrollY * -0.002; // VERY slow movement (was too fast before)
    
    // Set camera position - always looking straight at the wall
    camera.position.set(0, targetY, CAMERA_Z);
    camera.lookAt(0, targetY, 0); // always look at the same Y level as camera
    
  }, [scrollY, camera]);

  return null;
};

// Main AboutDesign component
const AboutDesign = ({ onBack, onReady }) => {
  const [scrollY, setScrollY] = useState(0);
  const containerRef = useRef();

  useEffect(() => {
    if (onReady) onReady();
  }, [onReady]);

  const handleScroll = (e) => {
    setScrollY(e.target.scrollTop);
  };

  return (
    <div
      className="about-design-container"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        background: '#000000',
        zIndex: 10,
        margin: 0,
        padding: 0,
        overflow: 'hidden'
      }}
    >
      {/* Global CSS to hide scrollbars */}
      <style jsx global>{`
        .content-overlay::-webkit-scrollbar {
          display: none;
        }
        .content-overlay {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
      {/* Back button */}
      <button
        className="back-button"
        onClick={onBack}
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          zIndex: 1000,
          background: 'rgba(255, 255, 255, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.3)',
          color: 'white',
          padding: '10px 20px',
          borderRadius: '5px',
          cursor: 'pointer',
          backdropFilter: 'blur(10px)',
          pointerEvents: 'auto'
        }}
      >
        ← Back
      </button>

      {/* Fullscreen 3D Scene */}
      <div
        className="scene-container"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          background: '#000000',
          margin: 0,
          padding: 0,
          overflow: 'hidden',
          pointerEvents: 'auto'
        }}
      >
        <Canvas
          shadows
          dpr={[1, 1.5]}
          camera={{ position: [0, 0, CAMERA_Z], fov: 45 }} // start at center level
          style={{
            background: '#000000',
            width: '100vw',
            height: '100vh',
            display: 'block'
          }}
          gl={{
            antialias: true,
            alpha: false,
            powerPreference: 'high-performance'
          }}
        >
          <CameraController scrollY={scrollY} />
          <SceneLights />
          <AboutBlocksModelGrid />

          {/* Disable all controls to prevent interference */}
          <OrbitControls enabled={false} />

          <EffectComposer>
            <Bloom luminanceThreshold={0} mipmapBlur luminanceSmoothing={0.0} intensity={1} />
            <DepthOfField target={[0, 0, 0]} focalLength={0.1} bokehScale={20} height={700} />
          </EffectComposer>
        </Canvas>
      </div>

      {/* SIMPLE SOLUTION: Only text areas have pointer-events auto, everything else is transparent */}
      <div
        ref={containerRef}
        className="content-overlay"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          overflowY: 'auto',
          overflowX: 'hidden',
          zIndex: 100,
          pointerEvents: 'auto', // Allow scrolling
          // Hide scrollbar
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
        onScroll={handleScroll}
      >
        {/* Section 1 - 100vh */}
        <div style={{ height: '100vh', display: 'flex', alignItems: 'center', paddingLeft: '10vw' }}>
          <div style={{ 
            color: 'white', 
            maxWidth: '500px',
            pointerEvents: 'none', // Make text transparent to pointer events
            userSelect: 'none' // Disable text selection
          }}>
            <h1 style={{
              fontSize: '4rem',
              fontWeight: 'bold',
              margin: '0 0 2rem 0',
              color: '#ffffff',
              fontFamily: 'Inter, sans-serif'
            }}>
              Hallo
            </h1>
            <h2 style={{
              fontSize: '2rem',
              fontWeight: '500',
              margin: '0 0 2rem 0',
              color: '#cccccc',
              fontFamily: 'Inter, sans-serif'
            }}>
              Wir sind BLCKS.
            </h2>
            <p style={{
              fontSize: '1.2rem',
              lineHeight: '1.6',
              margin: '0',
              color: '#999999',
              fontFamily: 'Inter, sans-serif'
            }}>
              We believe in the power of design to transform ideas into compelling visual experiences. Our approach combines creativity with technical excellence.
            </p>
          </div>
        </div>

        {/* Spacer 50vh */}
        <div style={{ height: '50vh' }} />

        {/* Section 2 - 150vh */}
        <div style={{ height: '150vh', display: 'flex', alignItems: 'center', paddingLeft: '10vw' }}>
          <div style={{ 
            color: 'white', 
            maxWidth: '800px',
            pointerEvents: 'none',
            userSelect: 'none'
          }}>
            <h1 style={{
              fontSize: '4rem',
              fontWeight: 'bold',
              margin: '0 0 4rem 0',
              color: '#ff6b6b',
              fontFamily: 'Inter, sans-serif'
            }}>
              Our Services
            </h1>

            {[
              { title: "Brand Identity", subtitle: "Visual storytelling", description: "Creating memorable brand experiences that resonate with your audience" },
              { title: "Web Design", subtitle: "Digital excellence", description: "Modern, responsive websites that convert visitors into customers" },
              { title: "3D Visualization", subtitle: "Immersive content", description: "Bringing concepts to life with cutting-edge 3D technology" },
              { title: "Motion Graphics", subtitle: "Dynamic storytelling", description: "Engaging animations that capture attention and convey messages" },
              { title: "Print Design", subtitle: "Tangible impact", description: "Physical materials that make lasting impressions" },
              { title: "Consulting", subtitle: "Strategic guidance", description: "Expert advice to elevate your design strategy and execution" }
            ].map((service, index) => (
              <div key={index} style={{ marginBottom: '3rem', display: 'flex', alignItems: 'flex-start' }}>
                <div style={{ flex: '1' }}>
                  <h3 style={{
                    fontSize: '1.8rem',
                    fontWeight: '600',
                    margin: '0 0 0.5rem 0',
                    color: '#ffffff',
                    fontFamily: 'Inter, sans-serif'
                  }}>
                    {service.title}
                  </h3>
                  <h4 style={{
                    fontSize: '1.2rem',
                    fontWeight: '500',
                    margin: '0 0 1rem 0',
                    color: '#ff6b6b',
                    fontFamily: 'Inter, sans-serif'
                  }}>
                    {service.subtitle}
                  </h4>
                </div>
                <div style={{ flex: '1', marginLeft: '4rem' }}>
                  <p style={{
                    fontSize: '1rem',
                    lineHeight: '1.6',
                    margin: '0',
                    color: '#cccccc',
                    fontFamily: 'Inter, sans-serif'
                  }}>
                    {service.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Spacer 50vh */}
        <div style={{ height: '50vh' }} />

        {/* Section 3 - 100vh */}
        <div style={{ height: '100vh', display: 'flex', alignItems: 'center', paddingLeft: '10vw' }}>
          <div style={{ 
            color: 'white', 
            maxWidth: '600px',
            pointerEvents: 'none',
            userSelect: 'none'
          }}>
            <h1 style={{
              fontSize: '5rem',
              fontWeight: 'bold',
              margin: '0',
              color: '#4ecdc4',
              fontFamily: 'Inter, sans-serif'
            }}>
              Innovation Meets Design
            </h1>
          </div>
        </div>

        {/* Spacer 50vh */}
        <div style={{ height: '50vh' }} />

        {/* Section 4 - 100vh */}
        <div style={{ height: '100vh', display: 'flex', alignItems: 'center', paddingLeft: '10vw' }}>
          <div style={{ 
            color: 'white', 
            maxWidth: '800px',
            pointerEvents: 'none',
            userSelect: 'none'
          }}>
            <h1 style={{
              fontSize: '4rem',
              fontWeight: 'bold',
              margin: '0 0 3rem 0',
              color: '#45b7d1',
              fontFamily: 'Inter, sans-serif'
            }}>
              Featured Projects
            </h1>

            <div style={{ display: 'flex', gap: '3rem' }}>
              <div style={{ flex: '1' }}>
                <div style={{
                  width: '100%',
                  height: '200px',
                  background: 'linear-gradient(45deg, #45b7d1, #4ecdc4)',
                  borderRadius: '10px',
                  marginBottom: '1rem'
                }} />
                <h3 style={{
                  fontSize: '1.5rem',
                  fontWeight: '600',
                  margin: '0 0 1rem 0',
                  color: '#ffffff',
                  fontFamily: 'Inter, sans-serif'
                }}>
                  Project Alpha
                </h3>
                <p style={{
                  fontSize: '1rem',
                  lineHeight: '1.6',
                  margin: '0',
                  color: '#cccccc',
                  fontFamily: 'Inter, sans-serif'
                }}>
                  A revolutionary approach to user interface design that prioritizes accessibility and user experience.
                </p>
              </div>

              <div style={{ flex: '1' }}>
                <div style={{
                  width: '100%',
                  height: '200px',
                  background: 'linear-gradient(45deg, #ff6b6b, #feca57)',
                  borderRadius: '10px',
                  marginBottom: '1rem'
                }} />
                <h3 style={{
                  fontSize: '1.5rem',
                  fontWeight: '600',
                  margin: '0 0 1rem 0',
                  color: '#ffffff',
                  fontFamily: 'Inter, sans-serif'
                }}>
                  Project Beta
                </h3>
                <p style={{
                  fontSize: '1rem',
                  lineHeight: '1.6',
                  margin: '0',
                  color: '#cccccc',
                  fontFamily: 'Inter, sans-serif'
                }}>
                  Innovative 3D visualization techniques that bring architectural concepts to life with stunning realism.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Spacer 50vh */}
        <div style={{ height: '50vh' }} />

        {/* Section 5 - 100vh */}
        <div style={{ height: '100vh', display: 'flex', alignItems: 'center', paddingLeft: '10vw' }}>
          <div style={{ 
            color: 'white', 
            maxWidth: '600px',
            pointerEvents: 'none',
            userSelect: 'none'
          }}>
            <h1 style={{
              fontSize: '4rem',
              fontWeight: 'bold',
              margin: '0 0 2rem 0',
              color: '#96ceb4',
              fontFamily: 'Inter, sans-serif'
            }}>
              Let's Create Together
            </h1>
            <h2 style={{
              fontSize: '2.5rem',
              fontWeight: '600',
              margin: '0 0 2rem 0',
              color: '#feca57',
              fontFamily: 'Inter, sans-serif'
            }}>
              Ready to Start Your Project?
            </h2>
            <p style={{
              fontSize: '1.3rem',
              lineHeight: '1.6',
              margin: '0',
              color: '#cccccc',
              fontFamily: 'Inter, sans-serif'
            }}>
              We're passionate about turning your vision into reality. Let's collaborate to create something extraordinary that exceeds your expectations and delights your audience.
            </p>
          </div>
        </div>

        {/* Extra space to allow full scroll */}
        <div style={{ height: '100vh' }} />
      </div>
    </div>
  );
};

export default AboutDesign;